//
//  FootprintDetailView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/4.
//

import SwiftUI
import MapKit

struct FootprintDetailView: View {
    let footprint: UserFootprints

    @State private var cameraPosition: MapCameraPosition
    private let coordinates: [CLLocationCoordinate2D]
    @EnvironmentObject private var footprintsViewModel : FootprintsViewModel

    // 删除确认对话框
    @State private var showDeleteAlert = false

    // 获取当前用户ID
    private let currentUserId = UserDefaults.standard.string(forKey: "currentUserId") ?? ""

    // 导航控制
    @Environment(\.dismiss) private var dismiss
    
    init(footprint: UserFootprints) {
        self.footprint = footprint
        // 转换足迹点为坐标数组
        let coords = footprint.footPrints.map {
            CLLocationCoordinate2D(latitude: $0.latitude, longitude: $0.longitude)
        }
        self.coordinates = coords
        
        // 初始化 cameraPosition
        if let first = coords.first {
            let region = MKCoordinateRegion(
                center: first,
                span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
            )
            _cameraPosition = State(initialValue: .region(region))
        } else {
            _cameraPosition = State(initialValue: .camera(
                MapCamera(centerCoordinate: CLLocationCoordinate2D(latitude: 0, longitude: 0),
                          distance: 1000, heading: 0, pitch: 0)
            ))
        }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: Theme.Spacing.md) {
                // 地图视图
                mapView
                    .frame(height: 400)
                    .cornerRadius(Theme.CornerRadius.md)

            // 出行信息展示
            VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
                // 活动类型
                HStack {
                    Spacer()
                    
                    Image(systemName: "figure.walk")
                        .foregroundColor(.textPrimary)
                    Text(footprint.activityType.displayName)
                        .font(.title3Brand)
                        .foregroundColor(.textPrimary)
                    
                    Spacer()
                }
                .padding(Theme.Spacing.md)

                // 创建时间
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.textSecondary)
                    Text("创建时间:")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text(footprint.formattedCreatedAt)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }

                // 状态
                HStack {
                    Image(systemName: footprint.isFinished ? "checkmark.circle.fill" : "clock.arrow.circlepath")
                        .foregroundColor(footprint.isFinished ? .success : .warning)
                    Text(footprint.isFinished ? "状态:" : "状态:")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text(footprint.isFinished ? "已完成" : "进行中")
                        .font(.bodyBrand)
                        .foregroundColor(footprint.isFinished ? .success : .warning)
                }

                // 总距离
                HStack {
                    Image(systemName: "road.lanes")
                        .foregroundColor(.textSecondary)
                    Text("总距离:")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text(footprint.formattedDistance)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }

                // 时长
                HStack {
                    Image(systemName: "timer")
                        .foregroundColor(.textSecondary)
                    Text("时长:")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text(footprint.formattedDuration)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }

                // 结束时间
                HStack {
                    Image(systemName: "calendar")
                        .foregroundColor(.textSecondary)
                    Text("结束时间:")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text(footprint.formattedUpdatedAt)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }

                // 轨迹点数
                HStack {
                    Image(systemName: "mappin.and.ellipse")
                        .foregroundColor(.textSecondary)
                    Text("轨迹点数:")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                    Spacer()
                    Text("\(footprint.footPrints.count)")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }

                // 地点打卡数（如果有）
                if let locationCheckIns = footprint.locationCheckIns, !locationCheckIns.isEmpty {
                    HStack {
                        Image(systemName: "location.circle.fill")
                            .foregroundColor(.brandGreen)
                        Text("地点打卡:")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary)
                        Spacer()
                        Text("\(locationCheckIns.count) 个")
                            .font(.bodyBrand)
                            .foregroundColor(.brandGreen)
                    }
                }
            }
            .padding(Theme.Spacing.md)
            .padding(.horizontal, Theme.Spacing.lg)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(Color.cardBackground)
            )
                Spacer()
            }
            .padding()
        }
        .navigationTitle("足迹详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            // 只有当前用户ID等于足迹记录的用户ID时才显示删除按钮
            if currentUserId == footprint.userId {
                ToolbarItem(placement: .topBarTrailing) {
                    Button(action: {
                        showDeleteAlert = true
                    }) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                    }
                }
            }
        }
        .alert("确认删除足迹记录?", isPresented: $showDeleteAlert) {
            Button("删除", role: .destructive) {
                Task {
                    await deleteFootprint()
                }
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("删除后将无法恢复")
        }
        .onAppear(perform: autoZoom)
    }

    /// 删除足迹记录
    private func deleteFootprint() async {
        await footprintsViewModel.deleteFootprints(footprintId: footprint.id)

        // 如果删除成功，返回上一页
        if footprintsViewModel.errorMessage == nil {
            dismiss()
        }
    }

    /// 地图视图
    private var mapView: some View {
        ZStack(alignment: .bottomTrailing) {
            Map(position: $cameraPosition, interactionModes: .all) {
                // 折线
                if coordinates.count >= 2 {
                    // 外层绿色模糊线
                    MapPolyline(coordinates: coordinates)
                        .stroke(Color.accent.opacity(0.3), lineWidth: 2.5)

                    MapPolyline(coordinates: coordinates)
                        .stroke(Color.accent.opacity(0.5), lineWidth: 2.3)

                    MapPolyline(coordinates: coordinates)
                        .stroke(Color.accent.opacity(0.8), lineWidth: 1.9)

                    MapPolyline(coordinates: coordinates)
                        .stroke(Color.accent, lineWidth: 1.6)

                    // 内层白色清晰线
                    MapPolyline(coordinates: coordinates)
                        .stroke(.white, lineWidth: 1)
                }
                // 起点
                if let start = coordinates.first {
                    Annotation("起点", coordinate: start) {
                        Image(systemName: "flag.2.crossed.fill")
                            .foregroundColor(.brandGreen)
                    }
                }
                // 终点
                if let end = coordinates.last {
                    Annotation("终点", coordinate: end) {
                        Image(systemName: "mappin.circle.fill")
                            .foregroundColor(.red)
                            .background(Circle().fill(.white).frame(width: 20, height: 20))
                    }
                }

                // 关联的地点打卡标注
                if let locationCheckIns = footprint.locationCheckIns {
                    ForEach(locationCheckIns, id: \.id) { checkin in
                        Annotation(
                            checkin.position?.count ?? 0 > 7 ? String((checkin.position ?? "未知地点").prefix(7)) + "..." : (checkin.position ?? "未知地点"),
                            coordinate: CLLocationCoordinate2D(latitude: checkin.latitude, longitude: checkin.longitude),
                            anchor: .center
                        ) {
                            CheckinAnnotationView(checkin: checkin.toPlaceCheckin())
                        }
                    }
                }
            }
            .mapStyle(.standard(elevation: .flat,
                                emphasis: .muted,
                                pointsOfInterest: .including(.publicTransport),
                                showsTraffic: false))
            .onMapCameraChange(frequency: .onEnd) { context in
                // 可选：响应镜头变化逻辑
                // print(context.region)
            }

            // 自动缩放按钮
            Button(action: autoZoom) {
                Image(systemName: "location.viewfinder")
                    .font(.title2)
                    .padding(8)
                    .background(.white.opacity(0.3))
                    .clipShape(Circle())
                    .shadow(radius: 3)
            }
            .padding()
        }
    }

    /// 自动缩放到涵盖所有足迹点
    private func autoZoom() {
        guard !coordinates.isEmpty else { return }
        let rect = coordinates
            .map { MKMapRect(origin: MKMapPoint($0), size: MKMapSize(width: 0, height: 0) ) }
            .reduce(MKMapRect.null) { $0.union($1) }
        cameraPosition = .rect(rect)
    }
}

#Preview {
    FootprintDetailView(footprint: UserFootprints(
        id: "1",
        userId: "user1",
        footPrints: [
            FootprintPoint(latitude: 37.7749, longitude: -122.4194, timestamp: "2023-01-01T00:00:00Z"),
            FootprintPoint(latitude: 37.7789, longitude: -122.4294, timestamp: "2023-01-01T00:00:00Z"),
            FootprintPoint(latitude: 37.7849, longitude: -122.4394, timestamp: "2023-01-01T01:00:00Z")
        ],
        activityType: .walking,
        isFinished: true,
        totalDistance: 1.5,
        createdAt: Date(),
        updatedAt: Date(),
        locationCheckIns: nil
    ))
}
