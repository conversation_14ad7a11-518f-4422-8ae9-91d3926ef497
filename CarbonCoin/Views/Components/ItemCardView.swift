//
//  ItemCardView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/8.
//

import SwiftUI
import CoreLocation
import Kingfisher

// MARK: - 卡片显示样式枚举
enum ItemCardDisplayStyle {
    case full      // 完整版
    case compact   // 缩略版
}

// MARK: - 主要卡片视图
struct ItemCardView: View {
    let userItemCard: UserItemCard
    let displayStyle: ItemCardDisplayStyle

    // 控制详情页面显示
    @State private var showCardDetail = false

    // 便利属性，用于访问卡片信息
    private var card: ItemCard {
        userItemCard.card ?? ItemCard(
            id: "placeholder",
            cardType: .scenery,
            themeColor: nil,
            coinReward: 0,
            experienceReward: 0,
            description: "无法加载卡片信息",
            title: "错误",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "",
            location: "",
            latitude: nil,
            longitude: nil
        )
    }

    var body: some View {
        Group {
            switch displayStyle {
            case .full:
                FullItemCardView(userItemCard: userItemCard, card: card)
            case .compact:
                CompactItemCardView(userItemCard: userItemCard, card: card)
                    .onTapGesture {
                        showCardDetail = true
                    }
            }
        }
        .sheet(isPresented: $showCardDetail) {
            cardDetailSheet(userItemCard)
        }
    }
}

// MARK: - 卡片详情Sheet
func cardDetailSheet(_ userItemCard: UserItemCard) ->  some View {
    CardDetailSheetView(userItemCard: userItemCard)
        .presentationDetents([.large])
        .presentationDragIndicator(.visible)
        .presentationCornerRadius(40)
        .presentationBackground(.ultraThinMaterial)
}

// MARK: - 卡片详情Sheet内容视图
struct CardDetailSheetView: View {
    let userItemCard: UserItemCard
    @EnvironmentObject private var userItemCardViewModel: UserItemCardViewModel
    @State private var showDeleteAlert = false

    // 获取当前用户ID
    private let currentUserId = UserDefaults.standard.string(forKey: "currentUserId") ?? ""
    
    // 导航控制
    @Environment(\.dismiss) private var dismiss

    // 便利属性，用于访问卡片信息
    private var card: ItemCard {
        userItemCard.card ?? ItemCard(
            id: "placeholder",
            cardType: .scenery,
            themeColor: nil,
            coinReward: 0,
            experienceReward: 0,
            description: "无法加载卡片信息",
            title: "错误",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "",
            location: "",
            latitude: nil,
            longitude: nil
        )
    }

    var body: some View {
        NavigationStack {
            ZStack {
                ScrollView {
                    VStack(spacing: Theme.Spacing.lg) {
                        FullItemCardView(userItemCard: userItemCard, card: card)
                            .padding(.horizontal, Theme.Spacing.md)
                    }
                    .padding(.top, Theme.Spacing.lg)
                    .padding(.bottom, Theme.Spacing.xl)
                }
                .scrollContentBackground(.hidden)
            }
            .navigationTitle("卡片详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                // 只有当前用户ID等于卡片用户ID时才显示删除按钮
                if currentUserId == userItemCard.userId {
                    ToolbarItem(placement: .topBarTrailing) {
                        Button(action: {
                            showDeleteAlert = true
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                        }
                    }
                }
            }
            .alert("确认删除卡片?", isPresented: $showDeleteAlert) {
                Button("删除", role: .destructive) {
                    Task {
                        await userItemCardViewModel.removeUserItemCard(userId: currentUserId, cardId: card.id)

                        dismiss();
                    }
                }
                Button("取消", role: .cancel) {}
            } message: {
                Text("将会删除您持有的这张卡片")
            }
        }
    }
}


// MARK: - 完整版卡片视图
struct FullItemCardView: View {
    let userItemCard: UserItemCard
    let card: ItemCard

    @State private var uiImage: UIImage?

    var imageSize: CGFloat = 300

    var body: some View {
        VStack(alignment: .center, spacing: 8) {

            // 主题色圆点（不带删除按钮）
            if let themeColor = card.themeColor{
                Circle()
                    .fill(Color(hex: themeColor).opacity(0.5))
                    .frame(width: 20, height: 20)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 2)
                    )
                    .padding(.top, Theme.Spacing.lg)
            }

            // 主体图片区域
            HStack{
                Spacer()

                cardImageSection
                    .padding(.top, Theme.Spacing.xl)

                Spacer()
            }

            // 标题和描述区域
            cardContentSection
                .padding(.top, Theme.Spacing.xl)

            rewardContent
                .padding(.vertical, Theme.Spacing.lg)

            Spacer()

            // 底部信息区域
            cardBottomSection
                .padding(.bottom, Theme.Spacing.md)
                .padding(.horizontal, Theme.Spacing.md)
        }
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(themeBackgroundColor.opacity(0.3))
        )
        .overlay(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .stroke(Color.white.opacity(0.5), lineWidth: 0)
        )
        .task {
            await loadImage()
        }
        .cardShadow()
        .padding(.horizontal, Theme.Spacing.sm)
    }

    private func loadImage() async {
        guard let imageURL = URL(string: card.imageURL) else { return }

        // 下载图片并转换为UIimage
        do {
            let resource = KF.ImageResource(downloadURL: imageURL)
            let result = try await KingfisherManager.shared.retrieveImage(with: resource)
            uiImage = result.image
        } catch {
            print("Error: \(error)")
        }
    }

    // 主题背景色
    private var themeBackgroundColor: Color {
        if let themeColor = card.themeColor {
            return Color(hex: themeColor)
        }
        return Color.brandGreen
    }

    // 图片区域
    private var cardImageSection: some View {
        ZStack {
            if let image = uiImage?.paddedImage(padding: 30) {
                // 外层描边
                if card.cardType != .scenery,
                   let blackImage = image.monochromeImage(with: .white) {
                    Image(uiImage: blackImage)
                        .resizable()
                        .scaledToFit()
                        .frame(maxHeight: imageSize * 1.1)
                        .compositingGroup() // 平滑渲染
                }

                // 内层描边
                if card.cardType != .scenery,
                   let whiteImage = image.monochromeImage(with: .black) {
                    Image(uiImage: whiteImage)
                        .resizable()
                        .scaledToFit()
                        .frame(maxHeight: imageSize * 1.05)
                        .compositingGroup()
                }

                // 原图
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .frame(maxHeight: imageSize)
            } else {
                ProgressView()
                    .frame(height: imageSize)
            }
        }
        .cornerRadius(Theme.CornerRadius.md, corners: [.topLeft, .topRight])
    }

    // 内容区域
    private var cardContentSection: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 标题
            Text(card.title)
                .font(.title2Brand)
                .foregroundColor(.textPrimary)
                .lineLimit(2)
                .multilineTextAlignment(.center)

            // 描述
            Text(card.description)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .lineLimit(5)
                .multilineTextAlignment(.leading)
        }
        .padding(.horizontal, Theme.Spacing.lg)
        .padding(.top, Theme.Spacing.md)
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    // 奖励显示区域
    private func calculateIndices(coin: Int, experience: Int) -> (eco: String, pack: String) {
        let eco = Double(coin) * 3 + Double(experience) * 2
        let pack = Double(coin) * 2 + Double(experience) * 1.2

        let formattedEco = String(format: "%.0f", eco)
        let formattedPack = String(format: "%.0f", pack)

        return (formattedEco, formattedPack)
    }

    private var rewardContent: some View {
        let indices = calculateIndices(coin: card.coinReward, experience: card.experienceReward)

        return VStack(spacing: 8) {
            // 环保指数 + 包装指数
            HStack {
                HStack(spacing: 4) {
                    Image("reward-eco")
                        .renderingMode(.template)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24)

                    Text("环保指数：")
                        .font(.captionBrand)

                    Text("\(indices.eco)")
                        .font(.bodyBrand)
                        .monospacedDigit() // 将数字设置为等宽样式
                }

                Spacer()

                HStack(spacing: 4) {
                    Image("reward-pack")
                        .renderingMode(.template)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24)

                    Text("包装指数：")
                        .font(.captionBrand)

                    Text("\(indices.pack)")
                        .font(.bodyBrand)
                        .monospacedDigit()
                }
            }
            .padding(.horizontal, Theme.Spacing.lg)

            // 金币 + 经验
            HStack {
                HStack(spacing: 4) {
                    Image("reward-coin")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24)

                    Text("碳币奖励：")
                        .font(.captionBrand)
                        .monospacedDigit()

                    Text("\(card.coinReward)")
                        .font(.bodyBrand)
                }

                Spacer()

                HStack(spacing: 4) {
                    Image(systemName: "leaf.fill")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24)
                        .foregroundColor(.brandGreen)

                    Text("经验奖励：")
                        .font(.captionBrand)
                        .monospacedDigit()

                    Text("\(card.experienceReward)")
                        .font(.bodyBrand)
                }
            }
            .padding(.horizontal, Theme.Spacing.lg)
        }
    }

    // 底部信息区域
    private var cardBottomSection: some View {
        HStack {
            // 左下角：位置信息
            VStack(alignment: .leading, spacing: 2) {
                if !card.location.isEmpty {
                    Text(card.location)
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                        .lineLimit(1)
                }
            }

            Spacer()

            // 右下角：创建时间
            Text(formatDate(card.createdAt))
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.bottom, Theme.Spacing.md)
        .padding(.top, Theme.Spacing.sm)
    }
}

// MARK: - 缩略版卡片视图
struct CompactItemCardView: View {
    let userItemCard: UserItemCard
    let card: ItemCard

    var body: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 左侧：图片和标题
            HStack(spacing: Theme.Spacing.sm) {
                // 图片
                cardImageThumbnail

                // 标题
                Text(card.title)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .lineLimit(1)
            }

            Spacer()

            // 右侧：位置和时间信息
            VStack(alignment: .trailing, spacing: 2) {
                if !card.location.isEmpty {
                    Text(card.location)
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                        .lineLimit(1)
                }

                Text(formatDate(card.createdAt))
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(themeBackgroundColor.opacity(0.3))
        )
        .overlay(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .stroke(Color.white.opacity(0.1), lineWidth: 1)
        )
    }
    
    // 获取背景色
    private var themeBackgroundColor: Color {
        if let themeColor = card.themeColor {
            return Color(hex: themeColor)
        }
        return Color.cardBackground
    }

    // 缩略图
    private var cardImageThumbnail: some View {
        ZStack {
            if let url = URL(string: card.imageURL) {
                KFImage(url)
                    .cancelOnDisappear(true)  // 滚出屏幕时取消加载
                    .downsampling(size: CGSize(width: 40, height: 40)) // 下采样到指定尺寸
                    .scaleFactor(UIScreen.main.scale) // 在高分辨率上更加清晰
                    .fade(duration: 0.25) // 淡入淡出效果
                    .cacheOriginalImage() // 保存原始图像，方便直接进入详情视图查看
                    .placeholder {
                        ProgressView()
                            .frame(width: 40, height: 40)
                    }
                    .resizable()
                    .scaledToFill()
                    .frame(width: 40, height: 40)
                    .clipped()
                    .cornerRadius(Theme.CornerRadius.sm)
                    .overlay(
                        Group {
                            if card.cardType != .scenery {
                                RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                                    .stroke(Color.white, lineWidth: 1)
                                    .background(
                                        RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                                            .stroke(Color.black, lineWidth: 0.5)
                                            .padding(1)
                                    )
                            }
                        }
                    )
            } else {
                Color.gray
                    .frame(width: 40, height: 40)
                    .cornerRadius(Theme.CornerRadius.sm)
            }
        }
    }
}

// MARK: - 简易卡片视图
struct DragItemCardView: View {
    let userItemCard: UserItemCard
    let itemSize: CGFloat = 120

    // 便利属性，用于访问卡片信息
    private var card: ItemCard {
        userItemCard.card ?? ItemCard(
            id: "placeholder",
            cardType: .scenery,
            themeColor: nil,
            coinReward: 0,
            experienceReward: 0,
            description: "无法加载卡片信息",
            title: "错误",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "",
            location: "",
            latitude: nil,
            longitude: nil
        )
    }

    var body: some View {
        VStack(alignment: .center, spacing: Theme.Spacing.xs) {
            // 卡片图片缩略图
            cardImageThumbnail

            // 卡片类型和主题色标签
            cardTypeIndicator
            
            // 卡片标题
            Text(card.title)
                .font(.captionBrand)
                .fontWeight(.medium)
                .foregroundColor(.textPrimary)
                .lineLimit(2)
                .multilineTextAlignment(.center)
        }
        .frame(width: itemSize)
        .padding(Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(themeBackgroundColor.opacity(0.3))
        )
        .overlay(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .stroke(themeBackgroundColor.opacity(0.2), lineWidth: 1)
        )
    }

    // 主题背景色
    private var themeBackgroundColor: Color {
        if let themeColor = card.themeColor {
            return Color(hex: themeColor)
        }
        return Color.brandGreen
    }

    // 卡片图片缩略图
    private var cardImageThumbnail: some View {
        ZStack {
            if let image = card.image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .frame(height: itemSize * 0.75)
                    .clipped()
                    .cornerRadius(Theme.CornerRadius.sm)
                    .overlay(
                        // 为非风景照添加描边效果
                        Group {
                            if card.cardType != .scenery {
                                RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                                    .stroke(Color.white, lineWidth: 1)
                                    .background(
                                        RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                                            .stroke(Color.black, lineWidth: 1)
                                            .padding(1)
                                    )
                            }
                        }
                    )
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: itemSize * 0.75)
                    .cornerRadius(Theme.CornerRadius.sm)
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title3)
                            .foregroundColor(.white.opacity(0.6))
                    )
            }
        }
    }

    // 卡片类型指示器
    private var cardTypeIndicator: some View {
        HStack(spacing: Theme.Spacing.xs) {
            // 如果是购物卡片且有主题色，显示主题色圆点
            if card.cardType == .shopping, let themeColor = card.themeColor {
                Circle()
                    .fill(Color(hex: themeColor))
                    .frame(width: 10, height: 10)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 1)
                    )
            }
            
            // 卡片类型标签
            Text(card.cardType.displayName)
                .font(.caption2)
                .foregroundColor(.textPrimary)
                .padding(.horizontal, 4)
                .padding(.vertical, 2)
                .background(themeBackgroundColor.opacity(0.3))
                .cornerRadius(Theme.CornerRadius.sm)
        }
    }
}




// MARK: - 预览
struct ItemCardView_Previews: PreviewProvider {
    static let sampleCard = ItemCard(
        id: "sample-card-id",
        cardType: .shopping,
        themeColor: "4B7905",
        coinReward: 15,
        experienceReward: 8,
        description: "这是一个赛百味品牌的杯子，环保材质制作，适合日常使用。",
        title: "碳宠小炭",
        imageFileName: "",
        imageURL: "",
        createdAt: Date(),
        authorId: "sample_user",
        location: "杭州市余杭区碳币街道",
        latitude: 30.2741,
        longitude: 120.1551
    )

    static let sampleUserItemCard = UserItemCard(
        id: "sample-user-item-card-id",
        userId: "sample_user",
        cardId: "sample-card-id",
        acquiredAt: Date(),
        isAuthor: true,
        card: sampleCard,
        author: AuthorInfo(userId: "sample_user", nickname: "测试用户", avatarURL: nil)
    )

    static var previews: some View {
        VStack(spacing: Theme.Spacing.lg) {
            // 完整版预览
            ItemCardView(userItemCard: sampleUserItemCard, displayStyle: .full)
                .previewDisplayName("完整版卡片")

            // 缩略版预览
            ItemCardView(userItemCard: sampleUserItemCard, displayStyle: .compact)
                .previewDisplayName("缩略版卡片")
        }
        .padding()
        .background(Color.globalBackgroundGradient)
        .previewLayout(.sizeThatFits)
    }
}
