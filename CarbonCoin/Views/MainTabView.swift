//
//  MainTabView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct MainTabView: View {
    var body: some View {
        TabView {
            // 足迹页面
            FootprintView()
                .tabItem {
                    Image("footIcon")
                        .renderingMode(.template)
                    Text("足迹")
                }
                .tag(0)

            // 碳宠页面
            PetView()
                .tabItem {
                    Image("petIcon")
                        .renderingMode(.template)
                    Text("碳宠")
                }
                .tag(1)

            // 识别页面
            ScanView()
                .tabItem {
                    Image("scanIcon")
                        .renderingMode(.template)
                    Text("识别")
                }
                .tag(2)

            // 动态页面
            ChatView()
                .tabItem {
                    Image("chatIcon")
                        .renderingMode(.template)
                    Text("动态")
                }
                .tag(3)

            // 我的页面
            MyInfoView()
                .tabItem {
                    Image("meIcon")
                        .renderingMode(.template)
                    Text("我的")
                }
                .tag(4)
        }
        .tint(Color.accentColor.opacity(0.8)) // 设置选中状态的颜色
    }
}

#Preview {
    MainTabView()
}
