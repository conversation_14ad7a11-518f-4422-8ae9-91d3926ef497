//
//  CheckinManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import Foundation
import CoreLocation

// MARK: - 地点打卡管理器协议

/// 地点打卡管理器协议，定义地点打卡相关的API接口
protocol CheckinManagerProtocol {
    /// 创建地点打卡记录
    func createPlaceCheckin(userId: String, position: String?, latitude: Double, longitude: Double, photoURLs: [String]?, description: String?) async throws -> PlaceCheckin

    /// 查询地点打卡记录
    func queryPlaceCheckins(userId: String, startDate: Date?, endDate: Date?) async throws -> [PlaceCheckin]

    /// 更新地点打卡记录
    func updatePlaceCheckin(id: String, position: String?, latitude: Double?, longitude: Double?) async throws -> PlaceCheckin

    /// 删除地点打卡记录
    func deletePlaceCheckin(id: String) async throws -> Bool

    /// 查询地点打卡记录详情
    func queryPlaceCheckinDetail(id: String) async throws -> PlaceCheckinDetailResponse

    /// 查询地点打卡统计
    func queryPlaceCheckinStats(userId: String, startDate: Date?, endDate: Date?) async throws -> PlaceCheckinStatsResponse
}

// MARK: - 地点打卡管理器

/// 地点打卡管理器，负责处理地点打卡相关的网络请求
@MainActor
class CheckinManager: ObservableObject, CheckinManagerProtocol {

    // MARK: - Published Properties

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    // MARK: - Private Properties

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - Initialization

    init() {
        setupDateCoding()
    }

    // MARK: 创建地点打卡记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - position: 地点名称
    ///   - latitude: 纬度
    ///   - longitude: 经度
    ///   - photoURLs: 照片URL数组
    ///   - description: 用户描述
    /// - Returns: 创建的打卡记录
    func createPlaceCheckin(userId: String, position: String? = nil, latitude: Double, longitude: Double, photoURLs: [String]? = nil, description: String? = nil) async throws -> PlaceCheckin {
        guard !userId.isEmpty else {
            throw PlaceCheckinError.missingUserId
        }

        guard latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180 else {
            throw PlaceCheckinError.invalidCoordinates
        }

        isLoading = true
        errorMessage = nil

        do {
            let checkin = try await performCreatePlaceCheckinRequest(userId: userId, position: position, latitude: latitude, longitude: longitude, photoURLs: photoURLs, description: description)
            print("✅ 成功创建地点打卡记录: \(checkin.id)")
            isLoading = false
            return checkin
        } catch {
            let checkinError = mapError(error)
            errorMessage = checkinError.localizedDescription
            print("❌ 创建地点打卡记录失败: \(checkinError.localizedDescription)")
            isLoading = false
            throw checkinError
        }
    }

    // MARK: 查询地点打卡记录
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    /// - Returns: 打卡记录数组
    func queryPlaceCheckins(userId: String, startDate: Date? = nil, endDate: Date? = nil) async throws -> [PlaceCheckin] {
        guard !userId.isEmpty else {
            throw PlaceCheckinError.missingUserId
        }

        isLoading = true
        errorMessage = nil

        do {
            let checkins = try await performQueryPlaceCheckinsRequest(userId: userId, startDate: startDate, endDate: endDate)
            print("✅ 成功查询地点打卡记录: \(checkins.count) 条")
            isLoading = false
            return checkins
        } catch {
            let checkinError = mapError(error)
            errorMessage = checkinError.localizedDescription
            print("❌ 查询地点打卡记录失败: \(checkinError.localizedDescription)")
            isLoading = false
            throw checkinError
        }
    }

    // MARK: 更新地点打卡记录
    /// - Parameters:
    ///   - id: 打卡记录ID
    ///   - position: 地点名称
    ///   - latitude: 纬度
    ///   - longitude: 经度
    /// - Returns: 更新后的打卡记录
    func updatePlaceCheckin(id: String, position: String? = nil, latitude: Double? = nil, longitude: Double? = nil) async throws -> PlaceCheckin {
        guard !id.isEmpty else {
            throw PlaceCheckinError.missingCheckinId
        }

        if let lat = latitude, let lng = longitude {
            guard lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180 else {
                throw PlaceCheckinError.invalidCoordinates
            }
        }

        isLoading = true
        errorMessage = nil

        do {
            let checkin = try await performUpdatePlaceCheckinRequest(id: id, position: position, latitude: latitude, longitude: longitude)
            print("✅ 成功更新地点打卡记录: \(checkin.id)")
            isLoading = false
            return checkin
        } catch {
            let checkinError = mapError(error)
            errorMessage = checkinError.localizedDescription
            print("❌ 更新地点打卡记录失败: \(checkinError.localizedDescription)")
            isLoading = false
            throw checkinError
        }
    }

    // MARK: 删除地点打卡记录
    /// - Parameter id: 打卡记录ID
    /// - Returns: 删除是否成功
    func deletePlaceCheckin(id: String) async throws -> Bool {
        guard !id.isEmpty else {
            throw PlaceCheckinError.missingCheckinId
        }

        isLoading = true
        errorMessage = nil

        do {
            let success = try await performDeletePlaceCheckinRequest(id: id)
            print("✅ 成功删除地点打卡记录: \(id)")
            isLoading = false
            return success
        } catch {
            let checkinError = mapError(error)
            errorMessage = checkinError.localizedDescription
            print("❌ 删除地点打卡记录失败: \(checkinError.localizedDescription)")
            isLoading = false
            throw checkinError
        }
    }

    // MARK: 查询地点打卡记录详情
    /// - Parameter id: 打卡记录ID
    /// - Returns: 打卡记录详情
    func queryPlaceCheckinDetail(id: String) async throws -> PlaceCheckinDetailResponse {
        guard !id.isEmpty else {
            throw PlaceCheckinError.missingCheckinId
        }

        isLoading = true
        errorMessage = nil

        do {
            let detail = try await performQueryPlaceCheckinDetailRequest(id: id)
            print("✅ 成功查询地点打卡记录详情: \(id)")
            isLoading = false
            return detail
        } catch {
            let checkinError = mapError(error)
            errorMessage = checkinError.localizedDescription
            print("❌ 查询地点打卡记录详情失败: \(checkinError.localizedDescription)")
            isLoading = false
            throw checkinError
        }
    }

    // MARK: 查询地点打卡统计
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    /// - Returns: 打卡统计信息
    func queryPlaceCheckinStats(userId: String, startDate: Date? = nil, endDate: Date? = nil) async throws -> PlaceCheckinStatsResponse {
        guard !userId.isEmpty else {
            throw PlaceCheckinError.missingUserId
        }

        isLoading = true
        errorMessage = nil

        do {
            let stats = try await performQueryPlaceCheckinStatsRequest(userId: userId, startDate: startDate, endDate: endDate)
            print("✅ 成功查询地点打卡统计: \(userId)")
            isLoading = false
            return stats
        } catch {
            let checkinError = mapError(error)
            errorMessage = checkinError.localizedDescription
            print("❌ 查询地点打卡统计失败: \(checkinError.localizedDescription)")
            isLoading = false
            throw checkinError
        }
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // MARK: - Private Methods

    /// 设置日期编解码
    private func setupDateCoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)
        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 执行创建地点打卡记录请求
    private func performCreatePlaceCheckinRequest(userId: String, position: String?, latitude: Double, longitude: Double, photoURLs: [String]?, description: String?) async throws -> PlaceCheckin {
        guard let url = URL(string: baseURL + "location-checkins") else {
            throw PlaceCheckinError.serverError("无效的URL")
        }

        print("📍 创建地点打卡记录: \(url.absoluteString)")
        print("📍 用户ID: \(userId), 位置: \(position ?? "未指定"), 坐标: (\(latitude), \(longitude))")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = CreatePlaceCheckinRequest(userId: userId, position: position, latitude: latitude, longitude: longitude, photoURLs: photoURLs, description: description)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw PlaceCheckinError.networkError(URLError(.badServerResponse))
        }

        print("📥 创建地点打卡记录响应状态码: \(httpResponse.statusCode)")

        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 创建地点打卡记录响应内容: \(responseString)")
        }

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 201:
            break
        case 400:
            throw PlaceCheckinError.invalidCoordinates
        case 404:
            throw PlaceCheckinError.userNotFound
        case 500:
            throw PlaceCheckinError.serverError("服务器内部错误")
        default:
            throw PlaceCheckinError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let checkinResponse = try jsonDecoder.decode(PlaceCheckinResponse<PlaceCheckin>.self, from: data)

        if !checkinResponse.success {
            throw PlaceCheckinError.serverError(checkinResponse.error ?? "创建地点打卡记录失败")
        }

        guard let checkin = checkinResponse.data else {
            throw PlaceCheckinError.serverError("响应数据为空")
        }

        return checkin
    }

    /// 执行查询地点打卡记录请求
    private func performQueryPlaceCheckinsRequest(userId: String, startDate: Date?, endDate: Date?) async throws -> [PlaceCheckin] {
        var urlComponents = URLComponents(string: baseURL + "location-checkins")!
        var queryItems = [URLQueryItem(name: "userId", value: userId)]

        if let startDate = startDate {
            queryItems.append(URLQueryItem(name: "startDate", value: startDate.ISO8601Format()))
        }
        if let endDate = endDate {
            queryItems.append(URLQueryItem(name: "endDate", value: endDate.ISO8601Format()))
        }

        urlComponents.queryItems = queryItems

        guard let url = urlComponents.url else {
            throw PlaceCheckinError.serverError("无效的URL")
        }

        print("📍 查询地点打卡记录: \(url.absoluteString)")

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw PlaceCheckinError.networkError(URLError(.badServerResponse))
        }

        print("📥 查询地点打卡记录响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw PlaceCheckinError.missingUserId
        case 404:
            throw PlaceCheckinError.userNotFound
        case 500:
            throw PlaceCheckinError.serverError("服务器内部错误")
        default:
            throw PlaceCheckinError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let checkinResponse = try jsonDecoder.decode(PlaceCheckinResponse<[PlaceCheckin]>.self, from: data)

        if !checkinResponse.success {
            throw PlaceCheckinError.serverError(checkinResponse.error ?? "查询地点打卡记录失败")
        }

        return checkinResponse.data ?? []
    }

    /// 执行更新地点打卡记录请求
    private func performUpdatePlaceCheckinRequest(id: String, position: String?, latitude: Double?, longitude: Double?) async throws -> PlaceCheckin {
        guard let url = URL(string: baseURL + "location-checkins") else {
            throw PlaceCheckinError.serverError("无效的URL")
        }

        print("📍 更新地点打卡记录: \(url.absoluteString)")
        print("📍 记录ID: \(id)")

        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = UpdatePlaceCheckinRequest(id: id, position: position, latitude: latitude, longitude: longitude)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw PlaceCheckinError.networkError(URLError(.badServerResponse))
        }

        print("📥 更新地点打卡记录响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw PlaceCheckinError.invalidCoordinates
        case 404:
            throw PlaceCheckinError.checkinNotFound
        case 500:
            throw PlaceCheckinError.serverError("服务器内部错误")
        default:
            throw PlaceCheckinError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let checkinResponse = try jsonDecoder.decode(PlaceCheckinResponse<PlaceCheckin>.self, from: data)

        if !checkinResponse.success {
            throw PlaceCheckinError.serverError(checkinResponse.error ?? "更新地点打卡记录失败")
        }

        guard let checkin = checkinResponse.data else {
            throw PlaceCheckinError.serverError("响应数据为空")
        }

        return checkin
    }

    /// 执行删除地点打卡记录请求
    private func performDeletePlaceCheckinRequest(id: String) async throws -> Bool {
        guard let url = URL(string: baseURL + "location-checkins") else {
            throw PlaceCheckinError.serverError("无效的URL")
        }

        print("📍 删除地点打卡记录: \(url.absoluteString)")
        print("📍 记录ID: \(id)")

        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = DeletePlaceCheckinRequest(id: id)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw PlaceCheckinError.networkError(URLError(.badServerResponse))
        }

        print("📥 删除地点打卡记录响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw PlaceCheckinError.missingCheckinId
        case 404:
            throw PlaceCheckinError.checkinNotFound
        case 500:
            throw PlaceCheckinError.serverError("服务器内部错误")
        default:
            throw PlaceCheckinError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let deleteResponse = try jsonDecoder.decode(PlaceCheckinResponse<String>.self, from: data)
        return deleteResponse.success
    }

    /// 执行查询地点打卡记录详情请求
    private func performQueryPlaceCheckinDetailRequest(id: String) async throws -> PlaceCheckinDetailResponse {
        guard let url = URL(string: baseURL + "location-checkins/detail") else {
            throw PlaceCheckinError.serverError("无效的URL")
        }

        print("📍 查询地点打卡记录详情: \(url.absoluteString)")
        print("📍 记录ID: \(id)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = PlaceCheckinDetailRequest(id: id)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw PlaceCheckinError.networkError(URLError(.badServerResponse))
        }

        print("📥 查询地点打卡记录详情响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw PlaceCheckinError.missingCheckinId
        case 404:
            throw PlaceCheckinError.checkinNotFound
        case 500:
            throw PlaceCheckinError.serverError("服务器内部错误")
        default:
            throw PlaceCheckinError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let detailResponse = try jsonDecoder.decode(PlaceCheckinResponse<PlaceCheckinDetailResponse>.self, from: data)

        if !detailResponse.success {
            throw PlaceCheckinError.serverError(detailResponse.error ?? "查询地点打卡记录详情失败")
        }

        guard let detail = detailResponse.data else {
            throw PlaceCheckinError.serverError("响应数据为空")
        }

        return detail
    }

    /// 执行查询地点打卡统计请求
    private func performQueryPlaceCheckinStatsRequest(userId: String, startDate: Date?, endDate: Date?) async throws -> PlaceCheckinStatsResponse {
        guard let url = URL(string: baseURL + "location-checkins/stats") else {
            throw PlaceCheckinError.serverError("无效的URL")
        }

        print("📍 查询地点打卡统计: \(url.absoluteString)")
        print("📍 用户ID: \(userId)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        let requestBody = PlaceCheckinStatsRequest(userId: userId, startDate: startDate, endDate: endDate)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw PlaceCheckinError.networkError(URLError(.badServerResponse))
        }

        print("📥 查询地点打卡统计响应状态码: \(httpResponse.statusCode)")

        // 处理HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw PlaceCheckinError.missingUserId
        case 404:
            throw PlaceCheckinError.userNotFound
        case 500:
            throw PlaceCheckinError.serverError("服务器内部错误")
        default:
            throw PlaceCheckinError.serverError("HTTP \(httpResponse.statusCode)")
        }

        let statsResponse = try jsonDecoder.decode(PlaceCheckinResponse<PlaceCheckinStatsResponse>.self, from: data)

        if !statsResponse.success {
            throw PlaceCheckinError.serverError(statsResponse.error ?? "查询地点打卡统计失败")
        }

        guard let stats = statsResponse.data else {
            throw PlaceCheckinError.serverError("响应数据为空")
        }

        return stats
    }

    /// 映射错误类型
    private func mapError(_ error: Error) -> PlaceCheckinError {
        if let checkinError = error as? PlaceCheckinError {
            return checkinError
        }

        if error is URLError {
            return .networkError(error)
        }

        if error is DecodingError {
            return .decodingError(error)
        }

        return .unknown(error.localizedDescription)
    }
}
