//
//  PropScrollComponent.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/21.
//

import SwiftUI
import Foundation

// MARK: - 道具滚动组件
/// 可复用的道具滚动视图组件，用于在不同的界面中显示用户道具
struct PropScrollComponent: View {
    // MARK: - Properties
    
    // 拖拽处理回调
    let onDragStarted: (PropInfo) -> Void
    let onDragChanged: (PropInfo, DragGesture.Value) -> Void
    let onDragEnded: (PropInfo, DragGesture.Value) -> Void
    
    // 可选的筛选条件
    let dateFilter: DateInterval?
    
    // MARK: - Initialization
    init(
        onDragStarted: @escaping (PropInfo) -> Void,
        onDragChanged: @escaping (PropInfo, DragGesture.Value) -> Void,
        onDragEnded: @escaping (PropInfo, DragGesture.Value) -> Void,
        dateFilter: DateInterval? = nil
    ) {
        self.onDragStarted = onDragStarted
        self.onDragChanged = onDragChanged
        self.onDragEnded = onDragEnded
        self.dateFilter = dateFilter
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 道具数量显示
            headerView
            
            // 道具滚动视图
            propScrollView
        }
    }
    
    // MARK: - 标题栏
    private var headerView: some View {
        HStack {
            Text("我的道具")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)
            
            Spacer()
            
            Text("\(filteredProps.count) 个")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.bottom, Theme.Spacing.sm)
    }
    
    // MARK: - 道具滚动视图
    private var propScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Theme.Spacing.md) {
                ForEach(filteredProps) { propInfo in
                    DraggablePropThumbnail(
                        propInfo: propInfo,
                        onDragStarted: onDragStarted,
                        onDragChanged: onDragChanged,
                        onDragEnded: onDragEnded
                    )
                }
            }
            .padding(.horizontal, Theme.Spacing.md)
        }
        .frame(height: 160)
    }
    
    // MARK: - 计算属性
    
    /// 根据日期筛选条件过滤道具
    private var filteredProps: [PropInfo] {
        let allProps = PropInteractionManager.shared.getAllPropInfos()
        
        guard let dateFilter = dateFilter else {
            return allProps
        }
        
        // 注意：PropInfo 可能没有创建时间字段，这里需要根据实际情况调整
        // 如果 PropInfo 有 createdAt 字段，可以使用类似的筛选逻辑
        // 目前先返回所有道具，后续可以根据需要添加时间筛选
        return allProps
    }
}

// MARK: - 可拖拽道具缩略图
/// 可拖拽的道具缩略图组件
struct DraggablePropThumbnail: View {
    let propInfo: PropInfo
    let onDragStarted: (PropInfo) -> Void
    let onDragChanged: (PropInfo, DragGesture.Value) -> Void
    let onDragEnded: (PropInfo, DragGesture.Value) -> Void

    @State private var isDragging = false
    @GestureState private var gestureState = DragState.inactive

    // 拖拽状态枚举
    enum DragState {
        case inactive
        case longPressing
        case dragging(translation: CGSize)

        var translation: CGSize {
            switch self {
            case .dragging(let translation):
                return translation
            default:
                return .zero
            }
        }

        var isActive: Bool {
            switch self {
            case .inactive:
                return false
            default:
                return true
            }
        }
    }

    var body: some View {
        propContent
            .onTapGesture {
                // 只有在非拖拽状态下才允许点击
                if !gestureState.isActive && !isDragging {
                    print("点击了道具: \(propInfo.propTitle)")
                }
            }
    }

    // MARK: - 道具内容视图
    private var propContent: some View {
        VStack(alignment: .center, spacing: Theme.Spacing.xs) {
            // 道具动画
            LottieHelperView(
                fileName: "\(propInfo.propTitle).json",
                contentMode: .scaleAspectFit,
                playLoopMode: .loop,
                animationProgress: 1
            )
            .frame(width: 80, height: 80)
            .background(
                Circle()
                    .fill(Color.brandGreen.opacity(0.1))
                    .overlay(
                        Circle()
                            .stroke(Color.brandGreen.opacity(0.3), lineWidth: 1)
                    )
            )

            // 道具标题
            Text(propInfo.propTitle)
                .font(.captionBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)
                .lineLimit(1)
        }
        .frame(width: 120)
        .padding(Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                        .stroke(Color.brandGreen.opacity(isDragging ? 0.8 : 0.3), lineWidth: 1)
                )
        )
        .scaleEffect(gestureState.isActive ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: gestureState.isActive)
        .onDrag {
            // 系统级拖拽支持 - 提供带前缀的道具ID作为拖拽数据
            return NSItemProvider(object: DragManager.createDraggablePropId(propInfo.propId) as NSString)
        }
        .simultaneousGesture(
            // 使用 simultaneousGesture 避免与点击手势冲突
            LongPressGesture(minimumDuration: 0.5)
                .onEnded { _ in
                    // 长按开始拖拽模式
                    isDragging = true
                    onDragStarted(propInfo)
                    
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                }
        )
        .simultaneousGesture(
            DragGesture(minimumDistance: 15, coordinateSpace: .global)
                .onChanged { value in
                    if isDragging {
                        onDragChanged(propInfo, value)
                    }
                }
                .onEnded { value in
                    if isDragging {
                        onDragEnded(propInfo, value)
                        isDragging = false
                    }
                }
        )
    }
}

// MARK: - 预览
#Preview("道具滚动组件") {
    PropScrollComponent(
        onDragStarted: { _ in print("开始拖拽道具") },
        onDragChanged: { _, _ in print("拖拽道具中") },
        onDragEnded: { _, _ in print("结束拖拽道具") }
    )
    .padding()
    .background(Color.gray.opacity(0.1))
}
