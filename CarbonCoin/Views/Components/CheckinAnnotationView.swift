//
//  CheckinAnnotationView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import SwiftUI
import Kingfisher

struct CheckinAnnotationView: View {
    let checkin: PlaceCheckin
    
    @State private var coverImageURL: URL?
    private let imageSize: CGFloat = 40

    var body: some View {
        NavigationLink(destination: CheckinDetailView(checkin: checkin)) {
            VStack(spacing: 4) {
                Text(checkin.formattedCreatedAt)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.8))

                if let url = coverImageURL {
                    KFImage(url)
                        .placeholder {
                            placeholderView
                        }
                        .downsampling(size: CGSize(width: imageSize, height: imageSize))
                        .scaleFactor(UIScreen.main.scale)
                        .fade(duration: 0.25)
                        .resizable()
                        .scaledToFill()
                        .frame(width: imageSize, height: imageSize)
                        .clipped()
                        .cornerRadius(Theme.CornerRadius.md)
                } else {
                    // 没有 URL 时直接显示占位
                    placeholderView
                }
            }
        }
        .buttonStyle(.plain)
        .onAppear {
            if let first = checkin.photoURLs?.first,
               let url = URL(string: first) {
                coverImageURL = url
            }
        }
    }

    private var placeholderView: some View {
        Image("log-checkin")
            .resizable()
            .renderingMode(.template)
            .foregroundColor(.brandGreen)
            .frame(width: imageSize-5, height: imageSize-5)
            .padding(4)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)   // 圆角矩形
                    .fill(Color.brandGreen.opacity(0.3))
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
            )
    }
}

#Preview {
    CheckinAnnotationView(checkin: .default)
        .preferredColorScheme(.dark)
}
