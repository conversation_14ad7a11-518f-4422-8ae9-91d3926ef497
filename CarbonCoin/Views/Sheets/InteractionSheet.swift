//
//  InteractionSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/21.
//

import SwiftUI
import Foundation

// MARK: - 交互类型枚举
enum InteractionType: String, CaseIterable, Identifiable {
    case cards = "cards"
    case props = "props"

    var id: String { rawValue }

    var displayName: String {
        switch self {
        case .cards:
            return "卡片"
        case .props:
            return "道具"
        }
    }

    var iconName: String {
        switch self {
        case .cards:
            return "rectangle.stack"
        case .props:
            return "gift"
        }
    }
}

// MARK: - 统一交互Sheet视图
struct InteractionSheet: View {
    // MARK: - Properties
    @EnvironmentObject private var itemCardViewModel: ItemCardViewModel
    @EnvironmentObject private var userItemCardViewModel: UserItemCardViewModel
    @State private var sheetHeight: CGFloat = UIScreen.main.bounds.height / 4 // 初始高度
    @State private var selectedInteractionType: InteractionType = .cards
    @State private var selectedTimePeriod: TimePeriod = .week
    @State private var isDragging = false
    @State private var showDateFilter = false
    @State private var startDate: Date? = nil
    @State private var endDate: Date? = nil
    @AppStorage("currentUserId") private var currentUserId: String = ""

    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    private let fullHeight: CGFloat
    private let compactHeight: CGFloat

    // 拖拽处理回调
    let onCardDropped: (String, String) -> Void // (cardId, userId)
    let onPropDropped: (Int, String) -> Void // (propId, userId)

    // MARK: - Initialization
    init(
        onCardDropped: @escaping (String, String) -> Void,
        onPropDropped: @escaping (Int, String) -> Void,
    ) {
        self.onCardDropped = onCardDropped
        self.onPropDropped = onPropDropped

        // 计算高度
        self.fullHeight = screenHeight * 0.7
        self.compactHeight = screenHeight / 4
    }

    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 顶部控制栏
            topControlBar

            // 内容区域
            contentArea
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.thinMaterial)
                .ignoresSafeArea(edges: .bottom)
        )
        .frame(height: sheetHeight)
        .frame(width: UIScreen.main.bounds.width * 0.95)
        .gesture(sheetDragGesture)
        .onAppear {
            loadInitialData()
        }
    }

    // MARK: - 顶部控制栏
    private var topControlBar: some View {
        HStack {
            // Tab 切换按钮
            tabSwitcher

            Spacer()

            // 时间筛选按钮
            timeFilterButton
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
    }

    // MARK: - Tab 切换器
    private var tabSwitcher: some View {
        HStack(spacing: Theme.Spacing.xs) {
            ForEach(InteractionType.allCases) { type in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedInteractionType = type
                    }
                    // 添加触觉反馈
                    let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                    impactFeedback.impactOccurred()
                }) {
                    HStack(spacing: Theme.Spacing.xs) {
                        Image(systemName: type.iconName)
                            .font(.system(size: 14, weight: .medium))

                        Text(type.displayName)
                            .font(.captionBrand)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedInteractionType == type ? .textPrimary: .textSecondary)
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.vertical, Theme.Spacing.sm)
                    .glassEffectIfAvailable(
                        selectedInteractionType == type ? .regular : .clear,
                        tintColor: selectedInteractionType == type ? .brandGreen.opacity(0.3) : nil
                    )
                }
            }
        }
        .padding(Theme.Spacing.xs)
    }

    // MARK: - 时间筛选按钮
    private var timeFilterButton: some View {
        Button(action: {
            showDateFilter = true
        }) {
            Image(systemName: hasDateFilter ? "calendar.badge.clock" : "calendar")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(hasDateFilter ? .brandGreen : .textSecondary)
                .padding(Theme.Spacing.sm)
        }
        .glassEffectIfAvailable(.regular)
        .sheet(isPresented: $showDateFilter) {
            DateFilterSheet(
                startDate: $startDate,
                endDate: $endDate,
                onApply: {
                    // 应用日期筛选
                    print("应用日期筛选: \(String(describing: startDate)) - \(String(describing: endDate))")
                },
                onClear: {
                    // 清除日期筛选
                    startDate = nil
                    endDate = nil
                    print("清除日期筛选")
                }
            )
        }
    }

    // MARK: - 内容区域
    private var contentArea: some View {
        Group {
            switch selectedInteractionType {
            case .cards:
                cardContentView
            case .props:
                propContentView
            }
        }
        .animation(.easeInOut(duration: 0.3), value: selectedInteractionType)
    }

    // MARK: - 卡片内容视图
    private var cardContentView: some View {
        VStack(spacing: 0) {
            CardScrollComponent(
                onDragStarted: handleCardDragStarted,
                onDragChanged: handleCardDragChanged,
                onDragEnded: handleCardDragEnded,
                dateFilter: currentDateFilter
            )

            Spacer()
        }
    }

    // MARK: - 道具内容视图
    private var propContentView: some View {
        VStack(spacing: 0) {
            PropScrollComponent(
                onDragStarted: handlePropDragStarted,
                onDragChanged: handlePropDragChanged,
                onDragEnded: handlePropDragEnded,
                dateFilter: currentDateFilter
            )

            Spacer()
        }
    }

    // MARK: - 计算属性

    /// 是否有日期筛选条件
    private var hasDateFilter: Bool {
        startDate != nil || endDate != nil
    }

    /// 当前的日期筛选区间
    private var currentDateFilter: DateInterval? {
        guard let start = startDate else { return nil }
        let end = endDate ?? Date()
        return DateInterval(start: start, end: end)
    }

    // MARK: - Sheet 拖拽手势
    private var sheetDragGesture: some Gesture {
        DragGesture()
            .onChanged { value in
                let newHeight = sheetHeight - value.translation.height
                sheetHeight = max(compactHeight, min(fullHeight, newHeight))
            }
            .onEnded { value in
                withAnimation(.spring()) {
                    if value.translation.height > 100 {
                        sheetHeight = compactHeight
                    } else if value.translation.height < -100 {
                        sheetHeight = fullHeight
                    } else {
                        sheetHeight = sheetHeight > (compactHeight + fullHeight) / 2 ? fullHeight : compactHeight
                    }
                }
            }
    }

    // MARK: - 数据加载
    private func loadInitialData() {
        Task {
            await userItemCardViewModel.fetchUserItemCards(for: currentUserId)
        }
    }

    // MARK: - 卡片拖拽处理方法

    /// 处理卡片拖拽开始
    private func handleCardDragStarted(_ userItemCard: UserItemCard) {
        isDragging = true
        print("🎯 开始拖拽卡片: \(userItemCard.card?.title ?? "未知卡片")")

    }

    /// 处理卡片拖拽状态变化
    private func handleCardDragChanged(_ userItemCard: UserItemCard, _ value: DragGesture.Value) {
        isDragging = true
    }

    /// 处理卡片拖拽结束
    private func handleCardDragEnded(_ userItemCard: UserItemCard, _ value: DragGesture.Value) {
        isDragging = false

    }

    // MARK: - 道具拖拽处理方法

    /// 处理道具拖拽开始
    private func handlePropDragStarted(_ propInfo: PropInfo) {
        isDragging = true
        print("🎯 开始拖拽道具: \(propInfo.propTitle)")
    }

    /// 处理道具拖拽状态变化
    private func handlePropDragChanged(_ propInfo: PropInfo, _ value: DragGesture.Value) {
        isDragging = true
    }

    /// 处理道具拖拽结束
    private func handlePropDragEnded(_ propInfo: PropInfo, _ value: DragGesture.Value) {
        isDragging = false
        print("🎯 结束拖拽道具: \(propInfo.propTitle)")
    }
}

// MARK: - 预览
#Preview("统一交互Sheet") {
    InteractionSheet(
        onCardDropped: { cardId, userId in
            print("卡片 \(cardId) 拖拽到用户 \(userId)")
        },
        onPropDropped: { propId, userId in
            print("道具 \(propId) 拖拽到用户 \(userId)")
        }
    )
    .environmentObject(ItemCardViewModel())
    .environmentObject(UserItemCardViewModel())
    .background(Color.black)
}
