//
//  UserInfoUpdate.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/29.
//

import Foundation
import SwiftUI

// MARK: - 用户信息更新服务
class UserInfoUpdateService: ObservableObject {
    static let shared = UserInfoUpdateService()

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    private init() {
        // 配置JSON编码器
        jsonEncoder.dateEncodingStrategy = .iso8601
        jsonDecoder.dateDecodingStrategy = .iso8601
    }

    // MARK: - 获取用户信息

    /// 根据userId获取用户基本信息
    func getUserInfo(userId: String) async throws -> UserData {
        guard let url = URL(string: "\(baseURL)users?userId=\(userId)") else {
            throw UserInfoError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (data, urlResponse) = try await URLSession.shared.data(for: request)

            // 检查HTTP响应状态
            if let httpResponse = urlResponse as? HTTPURLResponse {
                print("获取用户信息响应状态码: \(httpResponse.statusCode)")

                guard httpResponse.statusCode == 200 else {
                    if httpResponse.statusCode == 404 {
                        throw UserInfoError.userNotFound
                    }
                    throw UserInfoError.serverError(httpResponse.statusCode)
                }
            }

            // 解析响应数据
            let response = try jsonDecoder.decode(UserInfoResponse.self, from: data)

            if response.success, let userData = response.data {
                print("获取用户信息成功: \(userData.nickname)")
                return userData
            } else {
                throw UserInfoError.responseError(response.error ?? "未知错误")
            }

        } catch let error as DecodingError {
            print("用户信息响应解析失败: \(error)")
            throw UserInfoError.decodingError
        } catch let error as UserInfoError {
            throw error
        } catch {
            print("获取用户信息网络请求失败: \(error)")
            throw UserInfoError.networkError(error)
        }
    }

    // MARK: - 更新用户信息

    /// 更新用户信息到云端
    func updateUserInfo(userId: String,
                       avatarURL: String? = nil,
                       nickname: String? = nil,
                       sharingLocation: Bool? = nil,
                       carbonCoins: Int? = nil,
                       backgroundURL: String? = nil) async throws -> UserData {
        guard let url = URL(string: "\(baseURL)users") else {
            throw UserInfoError.invalidURL
        }

        // 创建更新请求
        let updateRequest = UserUpdateRequest(
            userId: userId,
            avatarURL: avatarURL,
            nickname: nickname,
            lastActiveTime: Date(), // 总是更新最后活跃时间
            sharingLocation: sharingLocation,
            carbonCoins: carbonCoins,
            backgroundURL: backgroundURL
        )

        var request = URLRequest(url: url)
        request.httpMethod = "PATCH"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            // 编码请求体
            let requestData = try jsonEncoder.encode(updateRequest)
            request.httpBody = requestData

            // 打印请求信息用于调试
            if let requestString = String(data: requestData, encoding: .utf8) {
                print("📤 用户信息更新请求体: \(requestString)")
            }

            let (data, urlResponse) = try await URLSession.shared.data(for: request)

            // 检查HTTP响应状态
            if let httpResponse = urlResponse as? HTTPURLResponse {
                print("用户信息更新响应状态码: \(httpResponse.statusCode)")

                // 打印响应数据用于调试
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📥 用户信息更新响应内容: \(responseString)")
                }

                guard httpResponse.statusCode == 200 else {
                    if httpResponse.statusCode == 404 {
                        throw UserInfoError.userNotFound
                    }
                    throw UserInfoError.serverError(httpResponse.statusCode)
                }
            }

            // 解析响应数据
            let response = try jsonDecoder.decode(UserUpdateResponse.self, from: data)

            if response.success, let userData = response.data {
                print("✅ 用户信息更新成功: \(userData.nickname)")
                return userData
            } else {
                throw UserInfoError.responseError(response.error ?? "更新失败")
            }

        } catch let error as DecodingError {
            print("用户信息更新响应解析失败: \(error)")
            throw UserInfoError.decodingError
        } catch let error as UserInfoError {
            throw error
        } catch {
            print("用户信息更新网络请求失败: \(error)")
            throw UserInfoError.networkError(error)
        }
    }

    // MARK: - 便捷更新方法

    /// 更新用户昵称
    func updateNickname(userId: String, nickname: String) async throws -> UserData {
        return try await updateUserInfo(userId: userId, nickname: nickname)
    }

    /// 更新用户头像URL
    func updateAvatarURL(userId: String, avatarURL: String) async throws -> UserData {
        return try await updateUserInfo(userId: userId, avatarURL: avatarURL)
    }
    
    /// 更新用户的背景面板
    func updateBackgroundURL(userId: String, backgroundURL: String) async throws -> UserData {
        return try await updateUserInfo(userId: userId, backgroundURL: backgroundURL)
    }

    /// 更新位置共享设置
    func updateSharingLocation(userId: String, sharingLocation: Bool) async throws -> UserData {
        return try await updateUserInfo(userId: userId, sharingLocation: sharingLocation)
    }

    /// 更新碳币数量
    func updateCarbonCoins(userId: String, carbonCoins: Int) async throws -> UserData {
        return try await updateUserInfo(userId: userId, carbonCoins: carbonCoins)
    }

    /// 上传头像并更新用户信息
    func uploadAvatarAndUpdateUser(userId: String, imageData: Data) async throws -> UserData {
        // 先上传图片到COS获取URL
        let avatarURL = try await ImageShareService.shared.uploadImage(imageData)

        // 然后更新用户信息
        return try await updateAvatarURL(userId: userId, avatarURL: avatarURL)
    }
    
    /// 上传并更新用户的背景板信息
    func uploadBgAndUpdateUser(userId: String, imageData: Data) async throws -> UserData {
        let BgURL = try await ImageShareService.shared.uploadImage(imageData)
        
        // update BgURL locally
        return try await updateBackgroundURL(userId: userId, backgroundURL: BgURL)
    }
}

// MARK: - 用户信息错误类型
enum UserInfoError: LocalizedError {
    case invalidURL
    case networkError(Error)
    case serverError(Int)
    case userNotFound
    case responseError(String)
    case decodingError

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的请求URL"
        case .networkError(let error):
            return "网络请求失败: \(error.localizedDescription)"
        case .serverError(let code):
            return "服务器错误，状态码: \(code)"
        case .userNotFound:
            return "用户不存在"
        case .responseError(let message):
            return "请求失败: \(message)"
        case .decodingError:
            return "响应数据解析失败"
        }
    }
}
