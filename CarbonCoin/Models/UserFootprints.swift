//
//  UserFootprints.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import Foundation
import CoreLocation
import MapKit

// MARK: - 出行活动类型

/// 出行活动类型枚举
enum ActivityType: String, CaseIterable, Codable {
    case walking = "walking"    // 步行
    case cycling = "cycling"    // 骑行
    case bus = "bus"           // 公交
    case subway = "subway"     // 地铁

    /// 显示名称
    var displayName: String {
        switch self {
        case .walking:
            return "步行"
        case .cycling:
            return "骑行"
        case .bus:
            return "公交"
        case .subway:
            return "地铁"
        }
    }

    /// 图标名称
    var iconName: String {
        switch self {
        case .walking:
            return "figure.walk"
        case .cycling:
            return "bicycle"
        case .bus:
            return "bus"
        case .subway:
            return "tram"
        }
    }
}

// MARK: - 足迹点模型

/// 单个足迹点
struct FootprintPoint: Codable, Identifiable, Equatable {
    let id = UUID()
    let latitude: Double    // 纬度
    let longitude: Double   // 经度
    let timestamp: String   // 时间戳（ISO 8601格式）

    enum CodingKeys: String, CodingKey {
        case latitude, longitude, timestamp
    }

    /// 从CLLocation创建足迹点
    init(location: CLLocation) {
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.timestamp = ISO8601DateFormatter().string(from: location.timestamp)
    }

    /// 手动创建足迹点
    init(latitude: Double, longitude: Double, timestamp: Date = Date()) {
        self.latitude = latitude
        self.longitude = longitude
        self.timestamp = ISO8601DateFormatter().string(from: timestamp)
    }
    
    init(latitude: Double, longitude: Double, timestamp: String = "") {
        self.latitude = latitude
        self.longitude = longitude
        self.timestamp = timestamp
    }

    /// 转换为CLLocation
    var clLocation: CLLocation {
        let validDate = timestamp.parseISO8601Date() ?? Date()

        return CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
            altitude: 0,
            horizontalAccuracy: 5,
            verticalAccuracy: 5,
            timestamp: validDate
        )
    }

    /// 转换为CLLocationCoordinate2D
    var coordinate: CLLocationCoordinate2D {
        return CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    }

    /// 格式化时间戳
    var formattedTimestamp: String {
        guard let validDate = timestamp.parseISO8601Date() else {
            return timestamp
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return formatter.string(from: validDate)
    }
}

// MARK: - 用户足迹记录模型

/// 用户足迹记录
struct UserFootprints: Codable, Identifiable, Equatable {
    let id: String                      // 足迹记录ID
    let userId: String                  // 用户ID
    let footPrints: [FootprintPoint]    // 足迹点数组
    let activityType: ActivityType      // 出行活动类型
    let isFinished: Bool                // 是否已完成此次出行
    let totalDistance: Double           // 总距离（公里）
    let createdAt: Date                 // 创建时间
    let updatedAt: Date                 // 更新时间
    let locationCheckIns: [LocationCheckIn]? // 关联的地点打卡记录（可选）

    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    var formattedUpdatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: updatedAt)
    }

    /// 格式化的距离
    var formattedDistance: String {
        if totalDistance < 1.0 {
            return String(format: "%.0f米", totalDistance * 1000)
        } else {
            return String(format: "%.2f公里", totalDistance)
        }
    }

    /// 出行时长（小时）
    var duration: Double {
        guard let firstPoint = footPrints.first,
              let lastPoint = footPrints.last,
              let startTime = firstPoint.timestamp.parseISO8601Date(),
              let endTime = lastPoint.timestamp.parseISO8601Date() else {
            return 0
        }

        return endTime.timeIntervalSince(startTime) / 3600.0 // 转换为小时
    }

    /// 格式化的时长
    var formattedDuration: String {
        let totalMinutes = Int(duration * 60)
        let hours = totalMinutes / 60
        let minutes = totalMinutes % 60

        if hours > 0 {
            return "\(hours)小时\(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
}

// MARK: - API请求模型

/// 创建足迹记录请求
struct CreateFootprintsRequest: Codable {
    let userId: String
    let activityType: ActivityType
    let isFinished: Bool
    let footPrints: [FootprintPoint]

    init(userId: String, activityType: ActivityType, isFinished: Bool = false, footPrints: [FootprintPoint] = []) {
        self.userId = userId
        self.activityType = activityType
        self.isFinished = isFinished
        self.footPrints = footPrints
    }
}

/// 更新足迹记录请求
struct UpdateFootprintsRequest: Codable {
    let footprintId: String
    let footPrints: [FootprintPoint]?
    let activityType: ActivityType?
    let isFinished: Bool?

    init(footprintId: String, footPrints: [FootprintPoint]? = nil, activityType: ActivityType? = nil, isFinished: Bool? = nil) {
        self.footprintId = footprintId
        self.footPrints = footPrints
        self.activityType = activityType
        self.isFinished = isFinished
    }
}

/// 删除足迹记录请求
struct DeleteFootprintsRequest: Codable {
    let footprintId: String
}

/// 查询足迹记录详情请求
struct FootprintsDetailRequest: Codable {
    let footprintId: String
}

/// 查询足迹统计请求
struct FootprintsStatsRequest: Codable {
    let userId: String
    let startDate: String?
    let endDate: String?
    let activityType: ActivityType?

    init(userId: String, startDate: Date? = nil, endDate: Date? = nil, activityType: ActivityType? = nil) {
        self.userId = userId
        self.startDate = startDate?.ISO8601Format()
        self.endDate = endDate?.ISO8601Format()
        self.activityType = activityType
    }
}

// MARK: - API响应模型

/// 足迹记录API响应基础模型
struct FootprintsResponse<T: Codable>: Codable {
    let success: Bool
    let message: String?
    let data: T?
    let error: String?
}

/// 足迹记录详情响应（包含用户信息）
struct FootprintsDetailResponse: Codable {
    let id: String
    let userId: String
    let footPrints: [FootprintPoint]
    let activityType: ActivityType
    let isFinished: Bool
    let totalDistance: Double
    let createdAt: Date
    let updatedAt: Date
    let user: UserInfo
    let locationCheckIns: [LocationCheckIn]? // 关联的地点打卡记录

    struct UserInfo: Codable {
        let userId: String
        let nickname: String
        let avatarURL: String?
    }
}

/// 足迹统计响应
struct FootprintsStatsResponse: Codable {
    let totalTrips: Int           // 总出行次数
    let finishedTrips: Int        // 已结束次数
    let ongoingTrips: Int         // 进行中次数
    let totalDistance: Double     // 总距离（公里）
    let totalDuration: Double     // 总时长（小时）
    let activityStats: ActivityStats

    struct ActivityStats: Codable {
        let walking: ActivityStat
        let cycling: ActivityStat
        let bus: ActivityStat
        let subway: ActivityStat

        struct ActivityStat: Codable {
            let trips: Int        // 出行次数
            let distance: Double  // 总距离（公里）
            let duration: Double  // 总时长（小时）
        }
    }
}

// MARK: - 足迹错误类型

/// 足迹服务错误枚举
enum FootprintsError: Error, LocalizedError {
    case networkError(Error)
    case serverError(String)
    case decodingError(Error)
    case missingUserId
    case missingFootprintId
    case invalidActivityType
    case footprintNotFound
    case userNotFound
    case unknown(String)

    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "网络请求失败: \(error.localizedDescription)"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .decodingError(let error):
            return "数据解析失败: \(error.localizedDescription)"
        case .missingUserId:
            return "缺少用户ID"
        case .missingFootprintId:
            return "缺少足迹记录ID"
        case .invalidActivityType:
            return "无效的活动类型"
        case .footprintNotFound:
            return "足迹记录不存在"
        case .userNotFound:
            return "用户不存在"
        case .unknown(let message):
            return message
        }
    }
}
