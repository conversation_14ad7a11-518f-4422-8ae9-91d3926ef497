<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>top-level-items</key>
	<array>
		<dict>
			<key>destination</key>
			<dict>
				<key>location-parameters</key>
				<dict>
					<key>EndingColumnNumber</key>
					<string>0</string>
					<key>EndingLineNumber</key>
					<string>122</string>
					<key>StartingColumnNumber</key>
					<string>0</string>
					<key>StartingLineNumber</key>
					<string>121</string>
					<key>Timestamp</key>
					<string>780226255.041904</string>
				</dict>
				<key>rebasable-url</key>
				<dict>
					<key>base</key>
					<string>workspace</string>
					<key>payload</key>
					<dict>
						<key>relative-path</key>
						<string>CarbonCoin/Utilities/AppSettings.swift</string>
					</dict>
				</dict>
			</dict>
			<key>text-context</key>
			<dict>
				<key>focused</key>
				<string>        updateUsageDays()
</string>
				<key>leading</key>
				<string>    init() {
        loadSettings()
</string>
				<key>trailing</key>
				<string>        setupNotificationObservers()

        // 检查用户登录状态并自动同步
</string>
			</dict>
			<key>type</key>
			<string>bookmark</string>
		</dict>
		<dict>
			<key>destination</key>
			<dict>
				<key>location-parameters</key>
				<dict>
					<key>EndingColumnNumber</key>
					<string>0</string>
					<key>EndingLineNumber</key>
					<string>128</string>
					<key>StartingColumnNumber</key>
					<string>0</string>
					<key>StartingLineNumber</key>
					<string>127</string>
					<key>Timestamp</key>
					<string>780226255.041883</string>
				</dict>
				<key>rebasable-url</key>
				<dict>
					<key>base</key>
					<string>workspace</string>
					<key>payload</key>
					<dict>
						<key>relative-path</key>
						<string>CarbonCoin/Utilities/AppSettings.swift</string>
					</dict>
				</dict>
			</dict>
			<key>text-context</key>
			<dict>
				<key>focused</key>
				<string>        }
</string>
				<key>leading</key>
				<string>        Task {
            await checkAndAutoSync()
</string>
				<key>trailing</key>
				<string>    }

    /// 检查用户登录状态并自动同步
</string>
			</dict>
			<key>type</key>
			<string>bookmark</string>
		</dict>
	</array>
</dict>
</plist>
