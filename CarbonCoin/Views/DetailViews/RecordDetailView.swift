//
//  RecordDetailView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import SwiftUI

// MARK: - 日志详情视图

/// 日志详情视图，显示完整的日志信息，包括图片、描述、点赞、评论等
struct RecordDetailView: View {

    // MARK: - Properties

    /// 日志数据
    let log: UserLog

    /// 日志视图模型
    @ObservedObject var logViewModel: LogViewModel

    /// 当前用户ID（从AppSettings获取）
    @EnvironmentObject private var appSettings: AppSettings

    /// 显示操作菜单
    @State private var showActionSheet = false

    /// 显示删除确认
    @State private var showDeleteAlert = false

    /// 评论输入文本
    @State private var commentText = ""

    /// 是否显示评论输入框
    @State private var showCommentInput = false

    /// 导航返回
    @Environment(\.dismiss) private var dismiss

    // MARK: - Initialization

    init(log: UserLog, logViewModel: LogViewModel) {
        self.log = log
        self.logViewModel = logViewModel
    }

    // MARK: - Body

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: Theme.Spacing.lg) {
                // 头部信息
                headerView

                // 图片展示
                if let imageList = log.imageList, !imageList.isEmpty {
                    imageGalleryView(imageList)
                }

                // 描述内容
                if let description = log.description, !description.isEmpty {
                    descriptionView(description)
                }

                // 位置和时间信息
                locationAndTimeView

                // 点赞和评论操作
                interactionView

                // 评论列表
                commentsView
            }
            .padding(Theme.Spacing.lg)
        }
        .navigationTitle("日志详情")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                if log.userId == appSettings.userId {
                    Button(action: {
                        showActionSheet = true
                    }) {
                        Image(systemName: "ellipsis.circle")
                            .foregroundColor(.textPrimary)
                    }
                }
            }
        }
        .confirmationDialog("日志操作", isPresented: $showActionSheet, titleVisibility: .visible) {
            Button(log.isPublic ? "设为私密" : "设为公开") {
                togglePublicStatus()
            }
            Button("删除日志", role: .destructive) {
                showDeleteAlert = true
            }
            Button("取消", role: .cancel) { }
        }
        .alert("确认删除", isPresented: $showDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                deleteLog()
            }
        } message: {
            Text("删除后无法恢复，确定要删除这条日志吗？")
        }
        .sheet(isPresented: $showCommentInput) {
            commentInputView
                .presentationDetents([.fraction(0.3)])
                .presentationCornerRadius(40)
                .presentationBackground(.thinMaterial)
        }
    }

    // MARK: - Header View

    private var headerView: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 用户头像
            AsyncImage(url: URL(string: log.user?.avatarURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Circle()
                    .fill(Color.cardBackground)
                    .overlay(
                        Image(systemName: "person.fill")
                            .foregroundColor(.textTertiary)
                    )
            }
            .frame(width: 40, height: 40)
            .clipShape(Circle())

            VStack(alignment: .leading, spacing: 2) {
                // 用户昵称
                Text(log.user?.nickname ?? "未知用户")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                // 记录类型
                Text(recordTypeText)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()

            // 公开状态标识
            if log.isPublic {
                Image(systemName: "globe")
                    .font(.caption)
                    .foregroundColor(.brandGreen)
            } else {
                Image(systemName: "lock.fill")
                    .font(.caption)
                    .foregroundColor(.textTertiary)
            }
        }
    }

    // MARK: - Image Gallery View

    private func imageGalleryView(_ imageList: [String]) -> some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: Theme.Spacing.sm) {
                ForEach(imageList, id: \.self) { imageURL in
                    AsyncImage(url: URL(string: imageURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } placeholder: {
                        Rectangle()
                            .fill(Color.cardBackground)
                            .overlay(
                                ProgressView()
                            )
                    }
                    .frame(width: 200, height: 150)
                    .clipShape(RoundedRectangle(cornerRadius: Theme.CornerRadius.md))
                }
            }
            .padding(.horizontal, Theme.Spacing.lg)
        }
        .padding(.horizontal, -Theme.Spacing.lg)
    }

    // MARK: - Description View

    private func descriptionView(_ description: String) -> some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            Text("描述")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Text(description)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .lineSpacing(4)
        }
        .padding(Theme.Spacing.md)
        .glassCard()
    }

    // MARK: - Location and Time View

    private var locationAndTimeView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            Text("详细信息")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                // 位置信息
                if let locationText = getLocationText() {
                    HStack {
                        Image(systemName: "location.fill")
                            .foregroundColor(.brandGreen)
                        Text(locationText)
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                    }
                }

                // 时间信息
                HStack {
                    Image(systemName: "clock.fill")
                        .foregroundColor(.blue)
                    Text(formatDetailTime(log.createdAt))
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }

                // 特定类型的额外信息
                additionalInfoView
            }
        }
        .padding(Theme.Spacing.md)
        .glassCard()
    }

    // MARK: - Additional Info View
    private var additionalInfoView: some View {
        Group {
            switch log.recordType {
            case .trip:
                if let footprint = log.userFootprints {
                    HStack {
                        Image(systemName: "figure.walk")
                            .foregroundColor(.orange)
                        Text("距离: \(String(format: "%.1f", footprint.totalDistance)) 公里")
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                    }
                }  else {
                    EmptyView()
                }
            case .location:
                if let checkin = log.locationCheckIns {
                    HStack {
                        Image(systemName: "mappin.circle.fill")
                            .foregroundColor(.red)
                        Text("坐标: \(String(format: "%.4f", checkin.latitude)), \(String(format: "%.4f", checkin.longitude))")
                            .font(.captionBrand)
                            .foregroundColor(.textTertiary)
                    }
                }  else {
                    EmptyView()
                }
            case .recognition:
                if let card = log.cardAcquisitionRecord?.card {
                    HStack {
                        Image(systemName: "creditcard.fill")
                            .foregroundColor(.purple)
                        Text("卡片: \(card.title)")
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)
                    }
                }  else {
                    EmptyView()
                }
            }
        }
    }

    // MARK: - Interaction View

    private var interactionView: some View {
        HStack(spacing: Theme.Spacing.lg) {
            // 点赞按钮
            Button(action: {
                toggleLike()
            }) {
                HStack(spacing: Theme.Spacing.xs) {
                    Image(systemName: isLikedByCurrentUser ? "heart.fill" : "heart")
                        .foregroundColor(isLikedByCurrentUser ? .red : .textSecondary)
                    Text("\(logViewModel.getLikeCount(for: log.id))")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }
            }
            .buttonStyle(PlainButtonStyle())

            // 评论按钮
            Button(action: {
                showCommentInput = true
            }) {
                HStack(spacing: Theme.Spacing.xs) {
                    Image(systemName: "bubble")
                        .foregroundColor(.textSecondary)
                    Text("\(logViewModel.getCommentCount(for: log.id))")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }
            }
            .buttonStyle(PlainButtonStyle())

            Spacer()
        }
        .padding(Theme.Spacing.md)
        .glassCard()
    }

    // MARK: - Comments View

    private var commentsView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            if !logViewModel.getComments(for: log.id).isEmpty {
                Text("评论")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                ForEach(logViewModel.getComments(for: log.id)) { comment in
                    commentItemView(comment)
                }
            }
        }
    }

    // MARK: - Comment Item View

    private func commentItemView(_ comment: LogComment) -> some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            HStack {
                // 用户头像
                AsyncImage(url: URL(string: comment.user.avatarURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Circle()
                        .fill(Color.cardBackground)
                        .overlay(
                            Image(systemName: "person.fill")
                                .foregroundColor(.textTertiary)
                                .font(.caption)
                        )
                }
                .frame(width: 24, height: 24)
                .clipShape(Circle())

                // 用户昵称
                Text(comment.user.nickname)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)

                Spacer()

                // 时间
                Text(formatCommentTime(comment.createdAt))
                    .font(.captionBrand)
                    .foregroundColor(.textTertiary)

                // 删除按钮（仅评论作者可见）
                if comment.userId == appSettings.userId {
                    Button(action: {
                        deleteComment(comment)
                    }) {
                        Image(systemName: "trash")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }

            // 评论内容
            Text(comment.content)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .lineSpacing(2)
        }
        .padding(Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground.opacity(0.5))
        )
    }

    // MARK: - Comment Input View

    private var commentInputView: some View {
        NavigationView {
            VStack(spacing: Theme.Spacing.lg) {
                // 自定义样式的多行文本输入框
                VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                    TextField("写下你的评论...", text: $commentText, axis: .vertical)
                        .TextFieldBG()
                        .lineLimit(3...6)
                }

                Spacer()
            }
            .padding(Theme.Spacing.lg)
            .navigationTitle("添加评论")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        showCommentInput = false
                        commentText = ""
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("发送") {
                        submitComment()
                    }
                    .disabled(commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }

    // MARK: - Computed Properties

    /// 记录类型文本
    private var recordTypeText: String {
        switch log.recordType {
        case .location:
            return "地点打卡"
        case .recognition:
            return "卡片识别"
        case .trip:
            return "出行记录"
        }
    }

    /// 当前用户是否已点赞
    private var isLikedByCurrentUser: Bool {
        return logViewModel.isLogLikedByUser(logId: log.id, userId: appSettings.userId)
    }

    // MARK: - Helper Methods

    /// 获取位置文本
    private func getLocationText() -> String? {
        switch log.recordType {
        case .location:
            return log.locationCheckIns?.position
        case .recognition:
            return log.cardAcquisitionRecord?.card?.location
        case .trip:
            return "足迹记录"
        }
    }

    /// 格式化详细时间
    private func formatDetailTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        return formatter.string(from: date)
    }

    /// 格式化评论时间
    private func formatCommentTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            formatter.dateFormat = "HH:mm"
        } else if calendar.isDateInYesterday(date) {
            formatter.dateFormat = "昨天 HH:mm"
        } else {
            formatter.dateFormat = "MM-dd HH:mm"
        }

        return formatter.string(from: date)
    }

    // MARK: - Action Methods

    /// 切换点赞状态
    private func toggleLike() {
        Task {
            if isLikedByCurrentUser {
                await logViewModel.unlikeLog(logId: log.id, userId: appSettings.userId)
            } else {
                await logViewModel.likeLog(logId: log.id, userId: appSettings.userId)
            }
        }
    }

    /// 切换公开状态
    private func togglePublicStatus() {
        Task {
            await logViewModel.updateUserLog(
                logId: log.id,
                isPublic: !log.isPublic
            )
        }
    }

    /// 删除日志
    private func deleteLog() {
        Task {
            await logViewModel.deleteUserLog(logId: log.id, userId: appSettings.userId)
            dismiss()
        }
    }

    /// 提交评论
    private func submitComment() {
        guard !commentText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        Task {
            await logViewModel.addComment(
                logId: log.id,
                userId: appSettings.userId,
                content: commentText
            )

            commentText = ""
            showCommentInput = false
        }
    }

    /// 删除评论
    private func deleteComment(_ comment: LogComment) {
        Task {
            await logViewModel.deleteComment(
                commentId: comment.id,
                userId: appSettings.userId,
                logId: log.id
            )
        }
    }
}

// MARK: - Preview

#Preview {
    NavigationStack {
        RecordDetailView(
            log: UserLog(
                id: "1",
                userId: "user1",
                recordType: .location,
                recordId: "checkin1",
                imageList: ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
                description: "今天在这里发现了一个很棒的咖啡店，环境很好，咖啡也很香醇。和朋友一起度过了愉快的下午时光。",
                isPublic: true,
                createdAt: Date(),
                updatedAt: Date(),
                user: UserInfo(userId: "user1", nickname: "测试用户", avatarURL: nil),
                likes: [
                    LogLike(id: "like1", userId: "user2", createdAt: Date(), user: UserInfo(userId: "user2", nickname: "用户2", avatarURL: nil)),
                    LogLike(id: "like2", userId: "user3", createdAt: Date(), user: UserInfo(userId: "user3", nickname: "用户3", avatarURL: nil))
                ],
                comments: [
                    LogComment(id: "comment1", userId: "user2", content: "看起来很不错呢！", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user2", nickname: "用户2", avatarURL: nil)),
                    LogComment(id: "comment2", userId: "user3", content: "下次我也要去试试", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user3", nickname: "用户3", avatarURL: nil))
                ],
                locationCheckIns: LocationCheckIn(id: "checkin1", position: "余杭区城市街道", latitude: 30.0, longitude: 120.0, photoURLs: nil, description: nil, userFootprintsId: nil, createdAt: Date()),
                userFootprints: nil,
                cardAcquisitionRecord: nil
            ),
            logViewModel: LogViewModel()
        )
        .environmentObject(AppSettings())
    }
    .globalBackground()
}
