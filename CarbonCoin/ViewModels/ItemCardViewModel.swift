//
//  ItemCardViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import Foundation
import SwiftUI
import CoreLocation

// MARK: - 卡片管理视图模型
/// 统一管理卡片的创建、存储、加载等功能
@MainActor
class ItemCardViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 卡片列表
    @Published var cards: [ItemCard] = []

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    // MARK: - Private Properties

    private let userDefaultsKey = "SavedCards"
    private let itemCardManager = ItemCardManager()
    private lazy var userItemCardManager = UserItemCardViewModel()

    // MARK: - Initialization

    init() {
        loadCards()
        setupNotificationObserver()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Notification Setup

    /// 设置通知监听器
    private func setupNotificationObserver() {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("UserItemCardsDidSync"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let syncedCards = notification.object as? [ItemCard] {
                Task { @MainActor in
                    self?.updateCardsFromSync(syncedCards)
                }
            }
        }
    }

    /// 从同步数据更新卡片列表
    private func updateCardsFromSync(_ syncedCards: [ItemCard]) {
        // 本地与云端卡片ID集合
        let syncedIDs = Set(syncedCards.map(\.id))
        // 删除所有本地中但云端已移除的卡片
        var updatedCards = cards
        updatedCards.removeAll { !syncedIDs.contains($0.id) }

        // 合并云端数据：添加新卡片或更新已存在卡片
        for syncedCard in syncedCards {
            if let idx = updatedCards.firstIndex(where: { $0.id == syncedCard.id }) {
                updatedCards[idx] = syncedCard
            } else {
                updatedCards.append(syncedCard)
            }
        }

        // 按创建时间排序并保存
        cards = updatedCards.sorted { $0.createdAt > $1.createdAt }
        saveCards()
        print("✅ ItemCardViewModel 已同步，当前共有 \(cards.count) 张卡片")
    }

    // MARK: - Card Management Methods

    /// 保存卡片到云端和本地存储
    func saveCard(
        cardType: CardType = .scenery,
        themeColor: String? = nil,
        description: String,
        title: String,
        imageData: Data,
        location: String = "",
        latitude: Double? = nil,
        longitude: Double? = nil,
        ecoFriendly: Int = 100,
        packValue: Int = 100,
        remark: String? = nil
    ) async -> String? {

        isLoading = true
        errorMessage = nil

        // 获取当前用户ID
        @AppStorage("currentUserId") var currentUserId: String = ""
        let authorId = currentUserId.isEmpty ? "anonymous" : currentUserId

        // 生成唯一ID
        let cardId = UUID().uuidString

        do {
            // 1. 先上传图片到COS获取URL
            let imageURL = try await ImageShareService.shared.uploadImage(imageData)

            // 2. 保存图片到本地
            let imageFileName = saveImageToDocuments(imageData: imageData, id: cardId)

            // 3. 根据环保和包装指数计算奖励
            let (coinReward, experienceReward) = calculateRewards(
                cardType: cardType,
                ecoFriendly: ecoFriendly,
                packValue: packValue
            )

            // 4. 创建卡片到云端
            let createdCard = try await itemCardManager.createItemCard(
                cardType: cardType,
                themeColor: themeColor,
                coinReward: coinReward,
                experienceReward: experienceReward,
                title: title,
                description: description,
                imageFileName: imageFileName,
                imageURL: imageURL,
                authorId: authorId,
                location: location,
                latitude: latitude,
                longitude: longitude
            )

            // 5. 添加到本地存储
            cards.append(createdCard)
            saveCards()
            isLoading = false

            print("✅ 卡片创建成功: \(createdCard.id)")
            return createdCard.id

        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
            print("❌ 卡片创建失败: \(error)")
            print("❌ 错误详情: \(error.localizedDescription)")
            if let itemCardError = error as? ItemCardError {
                print("❌ ItemCardError 类型: \(itemCardError)")
            }
            return nil
        }
    }

    // MARK: - Single Card Update Methods (保留原有功能)

    /// 更新单个卡片的图片并保存到文件系统
    func updateImage(for card: ItemCard, newImage: UIImage) {
        guard let data = newImage.pngData() else { return }

        // 使用原有的文件名或生成新的文件名（确保使用 PNG 扩展名）
        let fileName: String
        if card.imageFileName.isEmpty {
            fileName = "\(card.id).png"
        } else {
            // 如果原文件名是 .jpg，改为 .png
            let baseName = card.imageFileName.replacingOccurrences(of: ".jpg", with: "").replacingOccurrences(of: ".png", with: "")
            fileName = "\(baseName).png"
        }

        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
            .first!.appendingPathComponent(fileName)

        do {
            try data.write(to: url)
            // 更新模型，包含新的字段
            let updatedCard = ItemCard(
                id: card.id,
                cardType: card.cardType,
                themeColor: card.themeColor,
                coinReward: card.coinReward,
                experienceReward: card.experienceReward,
                description: card.description,
                title: card.title,
                imageFileName: fileName,
                imageURL: card.imageURL,
                createdAt: card.createdAt,
                authorId: card.authorId,
                location: card.location,
                latitude: card.latitude,
                longitude: card.longitude
            )

            // 更新卡片列表中的对应卡片
            updateCard(updatedCard)
        } catch {
            print("保存图片失败: \(error)")
        }
    }

    /// 更新现有卡片
    func updateCard(_ updatedCard: ItemCard) {
        if let index = cards.firstIndex(where: { $0.id == updatedCard.id }) {
            cards[index] = updatedCard
            saveCards()
        }
    }

    /// 删除用户持有的卡片
    func deleteCard(_ card: ItemCard) async {
        @AppStorage("currentUserId") var currentUserId: String = ""
        guard !currentUserId.isEmpty else {
            errorMessage = "用户未登录"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            // 1. 调用后端API删除用户持有的卡片记录
            try await itemCardManager.deleteUserItemCard(userId: currentUserId, cardId: card.id)

            // 2. 删除本地图像文件（如果是作者创建的卡片）
            if card.authorId == currentUserId && !card.imageFileName.isEmpty {
                let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
                    .first!.appendingPathComponent(card.imageFileName)
                try? FileManager.default.removeItem(at: url)
            }

            // 3. 从本地数组中移除
            cards.removeAll { $0.id == card.id }
            saveCards()
            isLoading = false

            print("✅ 用户持有的卡片删除成功: \(card.id)")
        } catch {
            errorMessage = error.localizedDescription
            isLoading = false
            print("❌ 删除用户持有的卡片失败: \(error.localizedDescription)")
        }
    }

    /// 从云端加载用户持有的卡片
    func loadUserCards() async {
        @AppStorage("currentUserId") var currentUserId: String = ""
        guard !currentUserId.isEmpty else {
            errorMessage = "用户未登录"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let userItemCards = try await itemCardManager.getUserItemCards(userId: currentUserId)

            // 提取卡片信息
            self.cards = userItemCards.compactMap { $0.card }
            self.saveCards() // 同步到本地存储
            self.isLoading = false

            print("✅ 成功加载用户卡片: \(userItemCards.count) 张")

        } catch {
            self.errorMessage = error.localizedDescription
            self.isLoading = false
            print("❌ 加载用户卡片失败: \(error.localizedDescription)")
        }
    }

    /// 同步刷新用户卡片（从云端重新加载）
    func refreshCards() async {
        await loadUserCards()
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // MARK: - Private Helper Methods

    /// 保存卡片数据到UserDefaults
    private func saveCards() {
        if let data = try? JSONEncoder().encode(cards) {
            UserDefaults.standard.set(data, forKey: userDefaultsKey)
        }
    }

    /// 从 UserDefaults 加载卡片（本地缓存）
    private func loadCards() {
        if let data = UserDefaults.standard.data(forKey: userDefaultsKey),
           let savedCards = try? JSONDecoder().decode([ItemCard].self, from: data) {
            cards = savedCards
        }
    }

    /// 保存图像到 Documents 目录
    private func saveImageToDocuments(imageData: Data, id: String) -> String {
        let fileName = "\(id).png"
        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            .appendingPathComponent(fileName)
        try? imageData.write(to: url)
        return fileName // 只返回文件名
    }

    /// 根据卡片类型和环保指数计算奖励
    /// - Parameters:
    ///   - cardType: 卡片类型
    ///   - ecoFriendly: 环保指数 (1-100)
    ///   - packValue: 包装指数 (1-100)
    /// - Returns: (碳币奖励, 经验奖励)
    private func calculateRewards(cardType: CardType, ecoFriendly: Int, packValue: Int) -> (coinReward: Int, experienceReward: Int) {
        switch cardType {
        case .scenery:
            // 风景卡片固定奖励
            return (coinReward: 10, experienceReward: 5)

        case .shopping:
            // 购物卡片根据环保和包装指数计算奖励
            let averageScore = (ecoFriendly + packValue) / 2

            // 基础奖励
            let baseCoinReward = 10
            let baseExperienceReward = 5

            // 根据平均分数计算额外奖励
            let bonusMultiplier = Double(averageScore) / 100.0
            let coinBonus = Int(Double(baseCoinReward) * bonusMultiplier)
            let experienceBonus = Int(Double(baseExperienceReward) * bonusMultiplier)

            return (
                coinReward: baseCoinReward + coinBonus,
                experienceReward: baseExperienceReward + experienceBonus
            )
        }
    }

    /// 获取当前位置信息
    func getCurrentLocationInfo() async -> (location: String, latitude: Double?, longitude: Double?) {
        let locationManager = await UserLocationManager()
        let (clLocation, locationString) = await locationManager.getCurrentLocationInfo()

        if let clLocation = clLocation {
            return (locationString, clLocation.coordinate.latitude, clLocation.coordinate.longitude)
        } else {
            return (locationString, nil, nil)
        }
    }
}