//
//  UserLogModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import Foundation

// MARK: - 用户日志数据模型

/// 用户日志记录类型枚举
enum RecordType: String, Codable, CaseIterable {
    case location = "location"      // 地点打卡
    case trip = "trip"             // 出行记录
    case recognition = "recognition" // 卡片识别

    var displayName: String {
        switch self {
        case .location:
            return "地点打卡"
        case .trip:
            return "出行记录"
        case .recognition:
            return "卡片识别"
        }
    }
}

/// 用户日志主模型 - 匹配后端Prisma模型
struct UserLog: Codable, Identifiable {
    let id: String
    let userId: String
    let recordType: RecordType
    let recordId: String
    let imageList: [String]?
    let description: String?
    let isPublic: Bool
    let createdAt: Date
    let updatedAt: Date

    // 关联数据
    let user: UserInfo?
    let likes: [LogLike]?
    let comments: [LogComment]?

    // 根据recordType关联的具体记录
    let locationCheckIns: LocationCheckIn?
    let userFootprints: UserFootprints?
    let cardAcquisitionRecord: UserItemCard?

    enum CodingKeys: String, CodingKey {
        case id, userId, recordType, recordId, imageList, description, isPublic, createdAt, updatedAt
        case user, likes, comments
        case locationCheckIns = "LocationCheckIns"
        case userFootprints = "UserFootprints"
        case cardAcquisitionRecord = "CardAcquisitionRecord"
    }
}

/// 用户信息模型（用于日志中的用户数据）
struct UserInfo: Codable, Identifiable, Equatable {
    let userId: String
    let nickname: String
    let avatarURL: String?
    
    // Identifiable需要id标识，此处直接使用唯一的userId
    var id: String { userId }
    
    enum CodingKeys: String, CodingKey{
        case userId, nickname, avatarURL
    }
}

/// 点赞记录模型
struct LogLike: Codable, Identifiable {
    let id: String
    let userId: String
    let createdAt: Date
    let user: UserInfo
}

/// 评论记录模型
struct LogComment: Codable, Identifiable {
    let id: String
    let userId: String
    let content: String
    let replyTo: String?
    let createdAt: Date
    let user: UserInfo
}

/// 地点打卡信息模型（用于日志关联）
struct LocationCheckIn: Codable, Equatable {
    let id: String
    let position: String?
    let latitude: Double
    let longitude: Double
    let photoURLs: [String]?    // 多张照片的URL数组
    let description: String?    // 用户输入的描述
    let userFootprintsId: String? // 关联的出行记录ID
    let createdAt: Date

    /// 转换为 PlaceCheckin 模型
    func toPlaceCheckin() -> PlaceCheckin {
        return PlaceCheckin(
            id: id,
            userId: "", // 在日志上下文中，userId 通常从父级获取
            position: position,
            latitude: latitude,
            longitude: longitude,
            photoURLs: photoURLs,
            description: description,
            userFootprintsId: userFootprintsId,
            createdAt: createdAt,
            updatedAt: createdAt // 使用创建时间作为更新时间
        )
    }
}

// MARK: - API请求和响应模型

/// 创建用户日志请求模型
struct CreateUserLogRequest: Codable {
    let userId: String
    let recordType: RecordType
    let recordId: String
    let imageList: [String]?
    let description: String?
    let isPublic: Bool?

    init(userId: String,
         recordType: RecordType,
         recordId: String,
         imageList: [String]? = nil,
         description: String? = nil,
         isPublic: Bool = true) {
        self.userId = userId
        self.recordType = recordType
        self.recordId = recordId
        self.imageList = imageList
        self.description = description
        self.isPublic = isPublic
    }
}

/// 更新用户日志请求模型
struct UpdateUserLogRequest: Codable {
    let logId: String
    let imageList: [String]?
    let description: String?
    let isPublic: Bool?

    init(logId: String,
         imageList: [String]? = nil,
         description: String? = nil,
         isPublic: Bool? = nil) {
        self.logId = logId
        self.imageList = imageList
        self.description = description
        self.isPublic = isPublic
    }
}

/// 用户日志查询参数模型
struct UserLogQueryParams: Codable {
    let userId: String
    let recordType: RecordType?
    let isPublic: Bool?
    let startDate: String?
    let endDate: String?
    let page: Int?
    let limit: Int?

    init(userId: String,
         recordType: RecordType? = nil,
         isPublic: Bool? = nil,
         startDate: Date? = nil,
         endDate: Date? = nil,
         page: Int = 1,
         limit: Int = 20) {
        self.userId = userId
        self.recordType = recordType
        self.isPublic = isPublic
        self.startDate = startDate?.ISO8601Format()
        self.endDate = endDate?.ISO8601Format()
        self.page = page
        self.limit = limit
    }
}

/// 分页信息模型
struct PaginationInfo: Codable {
    let current: Int
    let total: Int
    let count: Int
    let limit: Int
    let hasNext: Bool
    let hasPrev: Bool
}

/// 用户日志列表响应模型
struct UserLogListResponse: Codable {
    let success: Bool
    let data: UserLogListData?
    let error: String?
}

struct UserLogListData: Codable {
    let logs: [UserLog]
    let pagination: PaginationInfo
}

/// 用户日志操作响应模型
struct UserLogOperationResponse: Codable {
    let success: Bool
    let message: String?
    let data: UserLog?
    let error: String?
}

/// 删除用户日志响应模型
struct DeleteUserLogResponse: Codable {
    let success: Bool
    let message: String?
    let error: String?
}

// MARK: - 点赞相关模型

/// 创建点赞请求模型
struct CreateLikeRequest: Codable {
    let logId: String
    let userId: String
}

/// 点赞操作响应模型
struct LikeOperationResponse: Codable {
    let success: Bool
    let message: String?
    let data: LogLike?
    let error: String?
}

/// 删除点赞响应模型
struct DeleteLikeResponse: Codable {
    let success: Bool
    let message: String?
    let error: String?
}

// MARK: - 评论相关模型

/// 创建评论请求模型
struct CreateCommentRequest: Codable {
    let logId: String
    let userId: String
    let content: String
    let replyTo: String?

    init(logId: String,
         userId: String,
         content: String,
         replyTo: String? = nil) {
        self.logId = logId
        self.userId = userId
        self.content = content
        self.replyTo = replyTo
    }
}

/// 评论操作响应模型
struct CommentOperationResponse: Codable {
    let success: Bool
    let message: String?
    let data: LogComment?
    let error: String?
}

/// 删除评论响应模型
struct DeleteCommentResponse: Codable {
    let success: Bool
    let message: String?
    let error: String?
}

// MARK: - 错误处理

/// 用户日志相关错误类型
enum UserLogError: LocalizedError {
    case invalidURL
    case networkError(Error)
    case serverError(Int)
    case logNotFound
    case userNotFound
    case invalidParameters
    case permissionDenied
    case responseError(String)
    case decodingError
    case alreadyLiked
    case likeNotFound
    case commentNotFound
    case commentTooLong
    case emptyContent

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的请求URL"
        case .networkError(let error):
            return "网络请求失败: \(error.localizedDescription)"
        case .serverError(let code):
            return "服务器错误，状态码: \(code)"
        case .logNotFound:
            return "日志记录不存在"
        case .userNotFound:
            return "用户不存在"
        case .invalidParameters:
            return "请求参数无效"
        case .permissionDenied:
            return "权限不足"
        case .responseError(let message):
            return "请求失败: \(message)"
        case .decodingError:
            return "响应数据解析失败"
        case .alreadyLiked:
            return "已经点赞过了"
        case .likeNotFound:
            return "点赞记录不存在"
        case .commentNotFound:
            return "评论不存在"
        case .commentTooLong:
            return "评论内容过长"
        case .emptyContent:
            return "评论内容不能为空"
        }
    }
}
