//
//  CardScrollComponent.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/21.
//

import SwiftUI
import Foundation

// MARK: - 卡片滚动组件
/// 可复用的卡片滚动视图组件，用于在不同的界面中显示用户卡片
struct CardScrollComponent: View {
    // MARK: - Properties
    @EnvironmentObject private var userItemCardViewModel: UserItemCardViewModel
    @AppStorage("currentUserId") private var currentUserId: String = ""
    
    // 拖拽处理回调
    let onDragStarted: (UserItemCard) -> Void
    let onDragChanged: (UserItemCard, DragGesture.Value) -> Void
    let onDragEnded: (UserItemCard, DragGesture.Value) -> Void
    
    // 可选的筛选条件
    let dateFilter: DateInterval?
    
    // MARK: - Initialization
    init(
        onDragStarted: @escaping (UserItemCard) -> Void,
        onDragChanged: @escaping (UserItemCard, DragGesture.Value) -> Void,
        onDragEnded: @escaping (UserItemCard, DragGesture.Value) -> Void,
        dateFilter: DateInterval? = nil
    ) {
        self.onDragStarted = onDragStarted
        self.onDragChanged = onDragChanged
        self.onDragEnded = onDragEnded
        self.dateFilter = dateFilter
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 卡片数量显示
            headerView
            
            // 卡片滚动视图
            cardScrollView
        }
    }
    
    // MARK: - 标题栏
    private var headerView: some View {
        HStack {
            Text("我的卡片")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)
            
            Spacer()
            
            Text("\(filteredCards.count) 张")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.bottom, Theme.Spacing.sm)
    }
    
    // MARK: - 卡片滚动视图
    private var cardScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Theme.Spacing.md) {
                ForEach(filteredCards) { userItemCard in
                    DraggableUserItemCardThumbnail(
                        userItemCard: userItemCard,
                        onDragStarted: onDragStarted,
                        onDragChanged: onDragChanged,
                        onDragEnded: onDragEnded
                    )
                }
            }
            .padding(.horizontal, Theme.Spacing.md)
        }
        .frame(height: 160)
    }
    
    // MARK: - 计算属性
    
    /// 根据日期筛选条件过滤卡片
    private var filteredCards: [UserItemCard] {
        guard let dateFilter = dateFilter else {
            return userItemCardViewModel.userItemCards
        }
        
        return userItemCardViewModel.userItemCards.filter { userItemCard in
            dateFilter.contains(userItemCard.acquiredAt)
        }
    }
}

// MARK: - 可拖拽用户卡片缩略图
struct DraggableUserItemCardThumbnail: View {
    let userItemCard: UserItemCard
    let onDragStarted: (UserItemCard) -> Void
    let onDragChanged: (UserItemCard, DragGesture.Value) -> Void
    let onDragEnded: (UserItemCard, DragGesture.Value) -> Void

    @State private var dragOffset = CGSize.zero
    @State private var isPressed = false
    @State private var isDragging = false
    @State private var showCardDetail = false
    @GestureState private var gestureState: DragState = .inactive

    // 便利属性，用于访问卡片信息
    private var card: ItemCard {
        userItemCard.card ?? ItemCard(
            id: "placeholder",
            cardType: .scenery,
            themeColor: nil,
            coinReward: 0,
            experienceReward: 0,
            description: "无法加载卡片信息",
            title: "错误",
            imageFileName: "",
            imageURL: "",
            createdAt: Date(),
            authorId: "",
            location: "",
            latitude: nil,
            longitude: nil
        )
    }

    // 拖拽状态枚举
    enum DragState {
        case inactive
        case longPressing
        case dragging(translation: CGSize)

        var translation: CGSize {
            switch self {
            case .dragging(let translation):
                return translation
            default:
                return .zero
            }
        }

        var isActive: Bool {
            switch self {
            case .inactive:
                return false
            default:
                return true
            }
        }
    }

    var body: some View {
        cardContent
            .onTapGesture {
                // 只有在非拖拽状态下才允许显示详情
                if !gestureState.isActive && !isDragging {
                    showCardDetail = true
                }
            }
            .sheet(isPresented: $showCardDetail) {
                cardDetailSheet(userItemCard)
            }
    }

    // MARK: - 卡片内容视图
    private var cardContent: some View {
        DragItemCardView(userItemCard: userItemCard)
            .scaleEffect(gestureState.isActive ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: gestureState.isActive)
            .onDrag {
                // 系统级拖拽支持 - 提供带前缀的卡片ID作为拖拽数据
                return NSItemProvider(object: DragManager.createDraggableCardId(card.id) as NSString)
            }
            .simultaneousGesture(
                // 长按手势，只有长按成功后才开始拖拽
                LongPressGesture(minimumDuration: 0.5)
                    .onEnded { _ in
                        isDragging = true
                        onDragStarted(userItemCard)
                        // 添加触觉反馈
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                    }
            )
            .simultaneousGesture(
                // 拖拽手势，只有在 isDragging 为 true 时才响应
                DragGesture(minimumDistance: 15 ,coordinateSpace: .global)
                    .onChanged { value in
                        // 只有在长按激活拖拽模式后才处理拖拽
                        if isDragging {
                            onDragChanged(userItemCard, value)
                        }
                    }
                    .onEnded { value in
                        if isDragging {
                            onDragEnded(userItemCard, value)
                            isDragging = false
                        }
                    }
            )
    }
}

// MARK: - 预览
#Preview("卡片滚动组件") {
    CardScrollComponent(
        onDragStarted: { _ in print("开始拖拽") },
        onDragChanged: { _, _ in print("拖拽中") },
        onDragEnded: { _, _ in print("结束拖拽") }
    )
    .environmentObject(UserItemCardViewModel())
    .padding()
    .background(Color.gray.opacity(0.1))
}
